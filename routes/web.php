<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\AnggotaController;
use App\Http\Controllers\SimpananController;
use App\Http\Controllers\PinjamanController;
use App\Http\Controllers\LaporanController;
use App\Http\Controllers\Member\SimpananController as MemberSimpananController;
use App\Http\Controllers\Member\PinjamanController as MemberPinjamanController;
use App\Http\Controllers\Member\RiwayatTransaksiController;
use App\Http\Controllers\RiwayatController;
use App\Http\Controllers\ImportController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::redirect('/', 'login')->name('root');

Route::middleware(['auth', 'verified'])->group(function () {

    Route::get('/dashboard', function () {
        return auth()->user()->role === 'admin'
            ? Inertia::render('Dashboard')
            : redirect()->route('member.simpanan.index');
    })->name('dashboard');

    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [ProfileController::class, 'edit'])->name('edit');
        Route::patch('/', [ProfileController::class, 'update'])->name('update');
        Route::delete('/', [ProfileController::class, 'destroy'])->name('destroy');
    });

    // Admin routes
    Route::prefix('admin')->name('admin.')->group(function () {
        // Member (Anggota) resource routes
        Route::resource('anggota', AnggotaController::class)->except(['show']);
        Route::get('anggota/{id}', [AnggotaController::class, 'show'])->name('anggota.show');

        // Savings (Simpanan) routes
        Route::get('simpanan', [SimpananController::class, 'index'])->name('simpanan.index');
        Route::get('simpanan/active-savings/{userId}', [SimpananController::class, 'getActiveSavings'])->name('simpanan.active-savings');

        // Loan (Pinjaman) routes
        Route::get('pinjaman', [PinjamanController::class, 'index'])->name('pinjaman.index');
        Route::get('pinjaman/active-loans/{userId}', [PinjamanController::class, 'getActiveLoans'])->name('pinjaman.active-loans');

        // Report (Laporan) routes
        Route::get('laporan', [LaporanController::class, 'index'])->name('laporan.index');
        Route::get('laporan/search-members', [LaporanController::class, 'searchMembers'])->name('laporan.search-members');

        // Import routes
        Route::prefix('import')->name('import.')->group(function () {
            Route::get('types', [ImportController::class, 'getImportableTypes'])->name('types');
            Route::get('template/{type}', [ImportController::class, 'downloadTemplate'])->name('template');
            Route::post('{type}', [ImportController::class, 'import'])->name('process');
        });

        // Transaction history (Riwayat) routes
        Route::prefix('riwayat')->name('riwayat.')->group(function () {
            Route::get('/', [RiwayatController::class, 'index'])->name('index');
            Route::prefix('{type}')->group(function () {
                Route::put('{id}', [RiwayatController::class, 'update'])->name('update');
                Route::delete('{id}', [RiwayatController::class, 'destroy'])->name('destroy');
            });
            Route::post('/bulk-destroy', [RiwayatController::class, 'bulkDestroy'])->name('bulkDestroy');
        });
    });

    // Member routes
    Route::prefix('member')->name('member.')->group(function () {
        Route::get('simpanan', [MemberSimpananController::class, 'index'])->name('simpanan.index');
        Route::get('pinjaman', [MemberPinjamanController::class, 'index'])->name('pinjaman.index');
        Route::get('riwayat', [RiwayatTransaksiController::class, 'index'])->name('riwayat.index');

        // Redirects for member routes
        Route::redirect('simpanan-anggota', 'member/simpanan')->name('simpanan.anggota');
        Route::redirect('pinjaman-anggota', 'member/pinjaman')->name('pinjaman.anggota');
        Route::redirect('riwayat-anggota', 'member/riwayat')->name('riwayat.anggota');
    });

    // Settings (Pengaturan) route
    Route::get('pengaturan', function () {
        return Inertia::render('Pengaturan/Index');
    })->name('pengaturan.index');
});

// Authentication routes
require __DIR__.'/auth.php';