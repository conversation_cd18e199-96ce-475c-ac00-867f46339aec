<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        User::create([
            'nik' => '123456789',
            'bagian' => 'Administrator',
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123!'),
            'role' => 'admin',
            'status' => 'Aktif',
            'tanggal_masuk' => now(),
            'email_verified_at' => now(),
        ]);

        $this->call([
            AnggotaSeeder::class,
        ]);

    }
}
