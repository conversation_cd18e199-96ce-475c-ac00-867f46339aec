<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class AnggotaSeeder extends Seeder
{
    private int $batchSize;

    public function __construct()
    {
        $this->batchSize = (int) env('SEEDER_BATCH_SIZE', 250);
    }

    public function run(): void
    {
        $start = microtime(true);
        $this->command->info('Starting anggota seeder...');

        $logPath = storage_path('logs/seeded_passwords.csv');
        $logHandle = fopen($logPath, 'w');
        if ($logHandle === false) {
            throw new \RuntimeException('Could not open password log file for writing: ' . $logPath);
        }
        fputcsv($logHandle, ['nik', 'email', 'plain_password']);

        DB::beginTransaction();

        try {
            $jsonPath = base_path('data/data_anggota_fixxx.json');
            if (!File::exists($jsonPath)) {
                throw new \RuntimeException('JSON file not found: ' . $jsonPath);
            }

            $jsonContent = File::get($jsonPath);
            $members = json_decode($jsonContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \RuntimeException('Error parsing JSON: ' . json_last_error_msg());
            }

            $batch = [];
            $emailMap = [];
            $usedNiks = array_flip(DB::table('users')->pluck('nik')->toArray());
            $now = now();
            $validStatuses = ['Aktif', 'Non-Aktif'];

            foreach ($members as $index => $member) {
                if (empty($member['nama']) && empty($member['bagian'])) {
                    $this->command->warn("Skipping record {$index}: Both nama and bagian are empty");
                    continue;
                }

                $baseEmail = $this->generateSlug($member['nama']) . '@koperasi.com';
                $email = $this->makeUniqueEmail($baseEmail, $emailMap);
                $emailMap[$email] = true;

                $nik = !empty($member['nik']) && is_numeric($member['nik'])
                    ? (string)$member['nik']
                    : 100000 + $index;

                $plainPassword = 'Member' . mt_rand(100000, 999999);

                while (isset($usedNiks[$nik])) {
                    $nik++;
                }
                $usedNiks[$nik] = true;

                $status = isset($member['status']) && in_array($member['status'], $validStatuses)
                    ? $member['status']
                    : 'Aktif';

                $tanggalMasuk = !empty($member['tanggal_masuk'])
                    ? $member['tanggal_masuk']
                    : $now->startOfYear()->format('Y-m-d');

                $batch[] = [
                    'nik' => $nik,
                    'name' => $member['nama'] ?? null,
                    'email' => $email,
                    'password' => Hash::make($plainPassword),
                    'bagian' => $member['bagian'] ?? null,
                    'role' => 'anggota',
                    'status' => $status,
                    'email_verified_at' => $now,
                    'tanggal_masuk' => $tanggalMasuk,
                    'tanggal_keluar' => null,
                    'alasan_keluar' => null,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];

                fputcsv($logHandle, [$nik, $email, $plainPassword]);

                if (count($batch) >= $this->batchSize) {
                    DB::table('users')->insert($batch);
                    $batch = [];
                    $this->command->info('Processed ' . ($index + 1) . ' records...');
                }
            }

            if (!empty($batch)) {
                DB::table('users')->insert($batch);
            }

            DB::commit();

            $duration = round(microtime(true) - $start, 2);
            $this->command->info('Successfully seeded ' . count($members) . ' anggota records in ' . $duration . ' seconds');
            $this->command->info('Plain text passwords for new members have been saved to: ' . $logPath);
        } catch (\Exception $e) {
            DB::rollBack();
            $this->command->error('Error: ' . $e->getMessage());
            throw $e;
        } finally {
            if ($logHandle) {
                fclose($logHandle);
            }
        }
    }

    private function generateSlug(string $name): string
    {
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '.', $name)));
        return preg_replace('/\.+/', '.', $slug);
    }

    private function makeUniqueEmail(string $baseEmail, array &$emailMap): string
    {
        if (!isset($emailMap[$baseEmail])) {
            return $baseEmail;
        }

        $counter = 1;
        $email = str_replace('@', $counter . '@', $baseEmail);

        while (isset($emailMap[$email])) {
            $counter++;
            $email = str_replace('@', $counter . '@', $baseEmail);
        }

        return $email;
    }
}