<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->date('tanggal_masuk')->after('status')->nullable();
            $table->date('tanggal_keluar')->after('tanggal_masuk')->nullable();
            $table->string('alasan_keluar', 255)->after('tanggal_keluar')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['tanggal_masuk', 'tanggal_keluar', 'alasan_keluar']);
            //
        });
    }
};
