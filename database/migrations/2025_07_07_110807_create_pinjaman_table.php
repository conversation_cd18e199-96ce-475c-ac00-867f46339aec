<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pinjaman_anggota', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('anggota_id');
            $table->foreign('anggota_id')->references('id')->on('users')->onDelete('cascade');
            $table->enum('jenis', ['umum', 'khusus', 'emergency']);
            $table->enum('tipe', ['setoran', 'pinjaman']);
            $table->decimal('jumlah', 15, 2);
            $table->string('keterangan', 255);
            $table->date('tanggal');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pinjaman_anggota');
    }
};
