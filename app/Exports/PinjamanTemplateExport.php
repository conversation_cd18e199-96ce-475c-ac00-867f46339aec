<?php

namespace App\Exports;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class PinjamanTemplateExport
{
    public function download()
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Template Import Pinjaman');

        $sheet->mergeCells('A1:A2');
        $sheet->setCellValue('A1', 'NIK Anggota');
        
        $sheet->mergeCells('B1:D1');
        $sheet->setCellValue('B1', 'PENGAJUAN PINJAMAN');
        
        $pinjamanHeaders = ['Umum', 'Khusus', 'Emergency'];
        foreach ($pinjamanHeaders as $index => $header) {
            $sheet->setCellValueByColumnAndRow($index + 2, 2, $header);
        }
        
        $nikHeaderStyle = [
            'alignment' => [
                'horizontal' => 'center',
                'vertical' => 'center',
            ],
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['argb' => 'FFD9EAD3'],
            ],
        ];
        $sheet->getStyle('A1:A2')->applyFromArray($nikHeaderStyle);
        
        $sheet->mergeCells('E1:G1');
        $sheet->setCellValue('E1', 'SETORAN ANGSURAN');
        
        foreach ($pinjamanHeaders as $index => $header) {
            $sheet->setCellValueByColumnAndRow($index + 5, 2, $header);
        }
        
        $mainHeaderStyle = [
            'font' => ['bold' => true],
            'alignment' => ['horizontal' => 'center'],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['argb' => 'FFD9EAD3'],
            ],
        ];
        $sheet->getStyle('B1:D1')->applyFromArray($mainHeaderStyle);
        $sheet->getStyle('E1:G1')->applyFromArray($mainHeaderStyle);
        
        $subHeaderStyle = [
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['argb' => 'FFE8F5E9'],
            ],
        ];
        $sheet->getStyle('B2:G2')->applyFromArray($subHeaderStyle);

        $sheet->mergeCells('H1:J1');
        $sheet->setCellValue('H1', 'JASA PINJAM (JSP)');
        $jspHeaders = ['JSP Umum', 'JSP Khusus', 'JSP Emergency'];
        foreach ($jspHeaders as $index => $header) {
            $sheet->setCellValueByColumnAndRow($index + 8, 2, $header);
        }
        $sheet->getStyle('H1:J1')->applyFromArray($mainHeaderStyle);
        $sheet->getStyle('H2:J2')->applyFromArray($subHeaderStyle);

        $sampleData = [
            [
                '020023',
                '5000000',
                '100000',
                '100000',
                '500000',
                '50000',
                '50000',
                '10000', // JSP Umum
                '2000',  // JSP Khusus
                '3000',  // JSP Emergency
            ],
        ];

        $row = 3;
        foreach ($sampleData as $data) {
            $col = 1;
            foreach ($data as $value) {
                $sheet->setCellValueByColumnAndRow($col, $row, $value);
                $col++;
            }
            $row++;
        }

        foreach (range('A', 'J') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }
        
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="template_import_pinjaman_' . now()->format('Ymd_His') . '.xlsx"');
        header('Cache-Control: max-age=0');

        $writer = new Xlsx($spreadsheet);
        try {
            $writer->save('php://output');
        } catch (\Exception $e) {
            exit('Error exporting file: ' . $e->getMessage());
        }
        exit;
    }
}
