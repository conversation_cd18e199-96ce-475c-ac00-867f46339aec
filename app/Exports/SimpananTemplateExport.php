<?php

namespace App\Exports;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class SimpananTemplateExport
{
    public function download()
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Template Import Simpanan');

        $sheet->mergeCells('A1:A2');
        $sheet->setCellValue('A1', 'NIK Anggota');
        
        $sheet->mergeCells('B1:F1');
        $sheet->setCellValue('B1', 'SETORAN');
        
        $setoranHeaders = ['Pokok', 'Wajib', 'Sukarela', 'Sihara', 'Deposito'];
        foreach ($setoranHeaders as $index => $header) {
            $sheet->setCellValueByColumnAndRow($index + 2, 2, $header);
        }
        
        $nikHeaderStyle = [
            'alignment' => [
                'horizontal' => 'center',
                'vertical' => 'center',
            ],
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['argb' => 'FFD9EAD3'],
            ],
        ];
        $sheet->getStyle('A1:A2')->applyFromArray($nikHeaderStyle);
        
        $sheet->mergeCells('G1:K1');
        $sheet->setCellValue('G1', 'PENGAMBILAN');
        
        foreach ($setoranHeaders as $index => $header) {
            $sheet->setCellValueByColumnAndRow($index + 7, 2, $header);
        }
        
        $mainHeaderStyle = [
            'font' => ['bold' => true],
            'alignment' => ['horizontal' => 'center'],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['argb' => 'FFD9EAD3'],
            ],
        ];
        $sheet->getStyle('B1:F1')->applyFromArray($mainHeaderStyle);
        $sheet->getStyle('G1:K1')->applyFromArray($mainHeaderStyle);
        
        $subHeaderStyle = [
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['argb' => 'FFE8F5E9'],
            ],
        ];
        $sheet->getStyle('B2:K2')->applyFromArray($subHeaderStyle);

        $sampleData = [
            [
                '020023',
                '10000000',
                '10000000',
                '10000000',
                '10000000',
                '10000000',
                '1000000',
                '1000000',
                '1000000',
                '1000000',
                '1000000',
            ],
        ];
        
        foreach ($sampleData as $row => $rowData) {
            foreach ($rowData as $col => $value) {
                $sheet->setCellValueByColumnAndRow($col + 1, $row + 3, $value);
            }
        }

        $sampleDataStyle = [
            'font' => ['color' => ['argb' => 'FF808080']],
        ];
        $sheet->getStyle('A3:K4')->applyFromArray($sampleDataStyle);
        
        $centerStyle = [
            'alignment' => [
                'horizontal' => 'center',
            ],
        ];
        $sheet->getStyle('A1:K4')->applyFromArray($centerStyle);

        $borderStyle = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['argb' => 'FF000000'],
                ],
            ],
        ];
        $sheet->getStyle('A1:K4')->applyFromArray($borderStyle);

        foreach (range('A', 'K') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="template_import_simpanan_' . now()->format('Ymd_His') . '.xlsx"');
        header('Cache-Control: max-age=0');

        $writer = new Xlsx($spreadsheet);
        try {
            $writer->save('php://output');
        } catch (\Exception $e) {
            exit('Error exporting file: ' . $e->getMessage());
        }
        exit;
    }
}