<?php

namespace App\Imports;

use App\Models\User;
use App\Models\PinjamanNew as Pinjaman;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class PinjamanImport
{
    private $imported = 0;
    private $errors = [];
    private $tanggal;
    
    public function __construct($filename = null)
    {
        $this->tanggal = now()->format('Y-m-d');
        
        if ($filename) {
            if (stripos($filename, 'pinjaman') === false) {
                throw new \Exception('File harus berisi kata "pinjaman" pada nama file');
            }
            
            if (preg_match('/(\d{8})(?:\.xlsx?)?$/i', $filename, $matches) || 
                preg_match('/(\d{4})(\d{2})(\d{2})/i', $filename, $matches)) {
                
                $dateString = $matches[0];
                
                if (strlen($dateString) === 8) {
                    try {
                        $date = \Carbon\Carbon::createFromFormat('Ymd', $dateString);
                        if ($date) {
                            $this->tanggal = $date->format('Y-m-d');
                        }
                    } catch (\Exception $e) {
                    }
                }
            }
        }
    }
    
    public function process($worksheet)
    {
        try {
            $rows = $worksheet->toArray();
            $header = array_shift($rows);

            if (count($header) < 7) {
                throw new \Exception('Jumlah kolom header tidak sesuai dengan template');
            }

            foreach ($rows as $index => $row) {
                $rowNumber = $index + 2;
                
                if (empty($row[0]) || !trim($row[0])) {
                    continue;
                }

                $row = array_pad($row, 7, null);
                
                $data = $this->mapRowToData($row);
                
                $hasData = false;
                foreach ($data as $key => $value) {
                    if ($key !== 'NIK Anggota' && $value > 0) {
                        $hasData = true;
                        break;
                    }
                }
                
                if (!$hasData) {
                    continue;
                }
                
                $validator = $this->validateRow($data, $rowNumber);

                if ($validator->fails()) {
                    $this->errors[$rowNumber] = $validator->errors()->all();
                    continue;
                }

                $this->processTransactions($data);
                $this->imported++;
            }

            return ['success' => true, 'imported' => $this->imported, 'errors' => $this->errors];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage(), 'errors' => $this->errors];
        }
    }
    
    private function mapRowToData($row)
    {
        $data = [];
        $data['NIK Anggota'] = $row[0] ?? null;

        $pinjamanTypes = ['Umum', 'Khusus', 'Emergency'];
        foreach ($pinjamanTypes as $index => $type) {
            $data["Pinjaman $type"] = $row[$index + 1] ?? null;
        }


        foreach ($pinjamanTypes as $index => $type) {
            $data["Setoran $type"] = $row[$index + 4] ?? null;
        }

        foreach ($pinjamanTypes as $index => $type) {
            $data["JSP $type"] = $row[$index + 7] ?? null;
        }

        return $data;
    }
    
    private function validateRow($data, $rowNumber)
    {
        $cleanNumeric = function($value) {
            if (is_numeric($value)) return $value;
            return (float) preg_replace('/[^0-9.]/', '', $value);
        };

        foreach ($data as $key => $value) {
            if ($key !== 'NIK Anggota' && $value !== null) {
                $data[$key] = $cleanNumeric($value);
            }
        }

        $validator = Validator::make($data, [
            'NIK Anggota' => 'required|exists:users,nik',
            'Pinjaman Umum' => 'nullable|numeric|min:0',
            'Pinjaman Khusus' => 'nullable|numeric|min:0',
            'Pinjaman Emergency' => 'nullable|numeric|min:0',
            'Setoran Umum' => 'nullable|numeric|min:0',
            'Setoran Khusus' => 'nullable|numeric|min:0',
            'Setoran Emergency' => 'nullable|numeric|min:0',
            'JSP Umum' => 'nullable|numeric|min:0',
            'JSP Khusus' => 'nullable|numeric|min:0',
            'JSP Emergency' => 'nullable|numeric|min:0',
        ], [
            'NIK Anggota.required' => "Baris {$rowNumber}: NIK Anggota harus diisi",
            'NIK Anggota.exists' => "Baris {$rowNumber}: Anggota dengan NIK :input tidak ditemukan",
            '*.numeric' => "Baris {$rowNumber}: :attribute harus berupa angka",
            '*.min' => "Baris {$rowNumber}: :attribute tidak boleh negatif",
        ]);
        
        $hasTransaction = false;
        foreach ($data as $key => $value) {
            if ($key !== 'NIK Anggota' && !empty($value) && is_numeric($value) && $value > 0) {
                $hasTransaction = true;
                break;
            }
        }
        
        if (!$hasTransaction) {
            $validator->errors()->add('transactions', "Baris {$rowNumber}: Minimal harus ada satu transaksi yang diisi");
        }
        
        return $validator;
    }
    
    private function processTransactions($data)
    {
        $user = User::where('nik', $data['NIK Anggota'])->first();
        
        DB::transaction(function () use ($user, $data) {
            $jspValues = [];
            
            // First, collect all JSP values
            $jspTypes = [
                'JSP Umum' => 'umum',
                'JSP Khusus' => 'khusus',
                'JSP Emergency' => 'emergency'
            ];
            
            foreach ($jspTypes as $jspKey => $jenis) {
                $jspValue = (float) ($data[$jspKey] ?? 0);
                if ($jspValue > 0) {
                    $jspValues[$jenis] = $jspValue;
                }
            }
            
            // Process all transactions
            $transactions = [
                'Pinjaman Umum' => ['jenis' => 'umum', 'tipe' => 'pinjaman'],
                'Pinjaman Khusus' => ['jenis' => 'khusus', 'tipe' => 'pinjaman'],
                'Pinjaman Emergency' => ['jenis' => 'emergency', 'tipe' => 'pinjaman'],
                'Setoran Umum' => ['jenis' => 'umum', 'tipe' => 'setoran'],
                'Setoran Khusus' => ['jenis' => 'khusus', 'tipe' => 'setoran'],
                'Setoran Emergency' => ['jenis' => 'emergency', 'tipe' => 'setoran'],
            ];
            
            // Track which JSP types we've used
            $usedJspTypes = [];
            
            // Process regular transactions first
            foreach ($transactions as $field => $config) {
                $amount = (float) ($data[$field] ?? 0);
                $jenis = $config['jenis'];
                
                // Skip if amount is 0 or negative
                if ($amount <= 0) continue;
                
                $pinjamanData = [
                    'anggota_id' => $user->id,
                    'jenis' => $jenis,
                    'tipe' => $config['tipe'],
                    'jumlah' => $amount,
                    'tanggal' => $this->tanggal,
                    'keterangan' => 'Import dari Excel - ' . $field,
                ];
                
                // Add JSP if available for this jenis and not used yet
                if (isset($jspValues[$jenis]) && !in_array($jenis, $usedJspTypes)) {
                    $pinjamanData['jsp'] = $jspValues[$jenis];
                    $usedJspTypes[] = $jenis;
                }
                
                Pinjaman::create($pinjamanData);
            }
            
            // Create JSP-only records for any remaining JSP that wasn't used
            foreach ($jspValues as $jenis => $jspValue) {
                if (!in_array($jenis, $usedJspTypes)) {
                    Pinjaman::create([
                        'anggota_id' => $user->id,
                        'jenis' => $jenis,
                        'tipe' => 'setoran', // Default to setoran for JSP-only
                        'jumlah' => 0,
                        'jsp' => $jspValue,
                        'tanggal' => $this->tanggal,
                        'keterangan' => 'Import dari Excel - JSP ' . ucfirst($jenis) . ' Only',
                    ]);
                }
            }
        });
    }
    
    public function getImportedCount()
    {
        return $this->imported;
    }
    
    public function getErrors()
    {
        return $this->errors;
    }
}