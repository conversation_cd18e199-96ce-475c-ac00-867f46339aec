<?php

namespace App\Imports;

use App\Models\User;
use App\Models\SimpananNew as Simpanan;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class SimpananImport
{
    private $imported = 0;
    private $errors = [];
    private $tanggal;
    
    public function __construct($filename = null)
    {
        $this->tanggal = now()->format('Y-m-d');
        
        if ($filename) {
            if (stripos($filename, 'simpanan') === false) {
                throw new \Exception('File harus berisi kata "simpanan" pada nama file');
            }
            
            if (preg_match('/(\d{8})(?:\.xlsx?)?$/i', $filename, $matches) || 
                preg_match('/(\d{4})(\d{2})(\d{2})/i', $filename, $matches)) {
                
                $dateString = $matches[0];
                
                if (strlen($dateString) === 8) {
                    try {
                        $date = \Carbon\Carbon::createFromFormat('Ymd', $dateString);
                        if ($date) {
                            $this->tanggal = $date->format('Y-m-d');
                        }
                    } catch (\Exception $e) {
                    }
                }
            }
        }
    }
    
    public function process($worksheet)
    {
        try {
            $rows = $worksheet->toArray();
            $header = array_shift($rows);

            if (count($header) < 11) {
                throw new \Exception('Jumlah kolom header tidak sesuai dengan template');
            }

            foreach ($rows as $index => $row) {
                $rowNumber = $index + 2;
                
                if (empty($row[0]) || !trim($row[0])) {
                    continue;
                }

                $row = array_pad($row, 11, null);
                
                $data = $this->mapRowToData($row);
                
                $hasData = false;
                foreach ($data as $key => $value) {
                    if ($key !== 'NIK Anggota' && $value > 0) {
                        $hasData = true;
                        break;
                    }
                }
                
                if (!$hasData) {
                    continue;
                }
                
                $validator = $this->validateRow($data, $rowNumber);

                if ($validator->fails()) {
                    $this->errors[$rowNumber] = $validator->errors()->all();
                    continue;
                }

                $hasData = false;
                foreach ($data as $key => $value) {
                    if ($key !== 'NIK Anggota' && $value > 0) {
                        $hasData = true;
                        break;
                    }
                }

                if ($hasData) {
                    $this->processTransactions($data);
                    $this->imported++;
                }
            }

            return ['success' => true, 'imported' => $this->imported, 'errors' => $this->errors];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage(), 'errors' => $this->errors];
        }
    }
    
    private function mapRowToData($row)
    {
        $data = [];
        $data['NIK Anggota'] = $row[0] ?? null;

        $setoranTypes = ['Pokok', 'Wajib', 'Sukarela', 'Sihara', 'Deposito'];
        foreach ($setoranTypes as $index => $type) {
            $data["Setoran $type"] = $row[$index + 1] ?? null;
        }

        
        foreach ($setoranTypes as $index => $type) {
            $data["Pengambilan $type"] = $row[$index + 6] ?? null;
        }

        return $data;
    }
    
    private function validateRow($data, $rowNumber)
    {
        $cleanNumeric = function($value) {
            if (is_numeric($value)) return $value;
            return (float) preg_replace('/[^0-9.]/', '', $value);
        };

        
        foreach ($data as $key => $value) {
            if ($key !== 'NIK Anggota' && $value !== null) {
                $data[$key] = $cleanNumeric($value);
            }
        }

        $validator = Validator::make($data, [
            'NIK Anggota' => 'required|exists:users,nik',
            'Setoran Pokok' => 'nullable|numeric|min:0',
            'Setoran Wajib' => 'nullable|numeric|min:0',
            'Setoran Sukarela' => 'nullable|numeric|min:0',
            'Setoran Sihara' => 'nullable|numeric|min:0',
            'Setoran Deposito' => 'nullable|numeric|min:0',
            'Pengambilan Pokok' => 'nullable|numeric|min:0',
            'Pengambilan Wajib' => 'nullable|numeric|min:0',
            'Pengambilan Sukarela' => 'nullable|numeric|min:0',
            'Pengambilan Sihara' => 'nullable|numeric|min:0',
            'Pengambilan Deposito' => 'nullable|numeric|min:0',
        ], [
            'NIK Anggota.required' => "Baris {$rowNumber}: NIK Anggota harus diisi",
            'NIK Anggota.exists' => "Baris {$rowNumber}: Anggota dengan NIK :input tidak ditemukan",
            '*.numeric' => "Baris {$rowNumber}: :attribute harus berupa angka",
            '*.min' => "Baris {$rowNumber}: :attribute tidak boleh negatif",
        ]);
        
        $hasTransaction = false;
        foreach ($data as $key => $value) {
            if ($key !== 'NIK Anggota' && !empty($value) && is_numeric($value) && $value > 0) {
                $hasTransaction = true;
                break;
            }
        }
        
        if (!$hasTransaction) {
            $validator->errors()->add('transactions', "Baris {$rowNumber}: Minimal harus ada satu transaksi yang diisi");
        }
        
        return $validator;
    }
    
    private function processTransactions($data)
    {
        $user = User::where('nik', $data['NIK Anggota'])->first();
        
        DB::transaction(function () use ($user, $data) {
            $transactions = [
                'Setoran Pokok' => ['jenis' => 'pokok', 'tipe' => 'setoran'],
                'Setoran Wajib' => ['jenis' => 'wajib', 'tipe' => 'setoran'],
                'Setoran Sukarela' => ['jenis' => 'sukarela', 'tipe' => 'setoran'],
                'Setoran Sihara' => ['jenis' => 'sihara', 'tipe' => 'setoran'],
                'Setoran Deposito' => ['jenis' => 'deposito', 'tipe' => 'setoran'],
                'Pengambilan Pokok' => ['jenis' => 'pokok', 'tipe' => 'pengambilan'],
                'Pengambilan Wajib' => ['jenis' => 'wajib', 'tipe' => 'pengambilan'],
                'Pengambilan Sukarela' => ['jenis' => 'sukarela', 'tipe' => 'pengambilan'],
                'Pengambilan Sihara' => ['jenis' => 'sihara', 'tipe' => 'pengambilan'],
                'Pengambilan Deposito' => ['jenis' => 'deposito', 'tipe' => 'pengambilan'],
            ];
            
            foreach ($transactions as $field => $config) {
                $amount = (float) ($data[$field] ?? 0);
                if ($amount > 0) {
                    Simpanan::create([
                        'anggota_id' => $user->id,
                        'jenis' => $config['jenis'],
                        'tipe' => $config['tipe'],
                        'jumlah' => $amount,
                        'tanggal' => $this->tanggal,
                        'keterangan' => 'Import dari Excel - ' . $field,
                    ]);
                }
            }
        });
    }
    
    public function getImportedCount()
    {
        return $this->imported;
    }
    
    public function getErrors()
    {
        return $this->errors;
    }
}