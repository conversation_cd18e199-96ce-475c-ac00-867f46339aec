<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TransaksiPinjaman extends Model
{
    protected $table = 'transaksi_pinjaman';
    protected $primaryKey = 'id_transaksi';
    public $timestamps = true;

    protected $fillable = [
        'pinjaman_id',
        'tipe_transaksi',
        'jumlah',
        'tanggal_transaksi',
        'keterangan'
    ];

    protected $casts = [
        'jumlah' => 'decimal:2',
        'tanggal_transaksi' => 'datetime',
    ];

    public function pinjaman(): BelongsTo
    {
        return $this->belongsTo(Pinjaman::class, 'pinjaman_id');
    }

    public function angsuran()
    {
        return $this->hasOne(Angsuran::class, 'transaksi_pinjaman_id', 'id_transaksi');
    }
}
