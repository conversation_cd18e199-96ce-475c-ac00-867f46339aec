<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class JenisPinjaman extends Model
{
    protected $table = 'jenis_pinjaman';
    protected $primaryKey = 'id_jenis_pinjaman';
    public $incrementing = true;
    public $timestamps = true;

    protected $fillable = [
        'nama_jenis',
        'deskripsi',
    ];

    public function pinjaman(): HasMany
    {
        return $this->hasMany(Pinjaman::class, 'id_jenis_pinjaman');
    }
}
