<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PembayaranPinjaman extends Model
{
    protected $table = 'pembayaran_pinjaman';
    public $timestamps = true;

    protected $fillable = [
        'id_pinjaman',
        'jumlah_bayar',
        'tanggal_pembayaran',
    ];

    protected $casts = [
        'jumlah_bayar' => 'decimal:2',
        'tanggal_pembayaran' => 'datetime',
    ];

    public function pinjaman(): BelongsTo
    {
        return $this->belongsTo(Pinjaman::class, 'id_pinjaman');
    }
}
