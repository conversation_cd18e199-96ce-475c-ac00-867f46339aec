<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Simpanan extends Model
{
    protected $table = 'simpanan';

    public $timestamps = true;

    protected $fillable = [
        'user_id',
        'id_jenis_simpanan',
        'tanggal_dibuat',
    ];

    protected $casts = [
        'tanggal_dibuat' => 'datetime',
    ];
    
    protected $appends = ['saldo'];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function jenisSimpanan(): BelongsTo
    {
        return $this->belongsTo(JenisSimpanan::class, 'id_jenis_simpanan');
    }

    public function transaksiSimpanan(): HasMany
    {
        return $this->hasMany(TransaksiSimpanan::class, 'simpanan_id');
    }

    /**
     * Calculate saldo from transactions
     */
    public function calculateSaldo(): float
    {
        $totalSetoran = $this->transaksiSimpanan()
            ->where('tipe_transaksi', 'Setoran')
            ->sum('jumlah');
        
        $totalPengambilan = $this->transaksiSimpanan()
            ->where('tipe_transaksi', 'Pengambilan')
            ->sum('jumlah');
        
        return (float) ($totalSetoran - $totalPengambilan);
    }

    /**
     * Get the calculated saldo attribute
     */
    public function getSaldoAttribute()
    {
        return $this->calculateSaldo();
    }
}
