<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TransaksiSimpanan extends Model
{
    protected $table = 'transaksi_simpanan';
    protected $primaryKey = 'id_transaksi';
    public $timestamps = true;

    protected $fillable = [
        'simpanan_id',
        'tipe_transaksi',
        'jumlah',
        'tanggal_transaksi',
        'keterangan',
    ];

    protected $casts = [
        'jumlah' => 'decimal:2',
        'tanggal_transaksi' => 'datetime',
    ];

    public function simpanan(): BelongsTo
    {
        return $this->belongsTo(Simpanan::class, 'simpanan_id');
    }
}
