<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SimpananNew extends Model
{
    protected $table = 'simpanan_anggota';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'anggota_id',
        'jenis',
        'tipe',
        'jumlah',
        'tanggal',
        'keterangan',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'tanggal' => 'date',
        'jumlah' => 'decimal:2',
    ];

    /**
     * Get the user that owns the simpanan.
     */
    public function anggota(): BelongsTo
    {
        return $this->belongsTo(User::class, 'anggota_id');
    }

    /**
     * Get the user that owns the simpanan.
     * Alias for anggota() to maintain compatibility with existing code.
     */
    public function user(): BelongsTo
    {
        return $this->anggota();
    }

    /**
     * Scope a query to only include setoran.
     */
    public function scopeSetoran($query)
    {
        return $query->where('tipe', 'setoran');
    }

    /**
     * Scope a query to only include pengambilan.
     */
    public function scopePengambilan($query)
    {
        return $query->where('tipe', 'pengambilan');
    }

    /**
     * Get the formatted amount attribute.
     */
    public function getFormattedJumlahAttribute(): string
    {
        return 'Rp ' . number_format($this->jumlah, 0, ',', '.');
    }
}
