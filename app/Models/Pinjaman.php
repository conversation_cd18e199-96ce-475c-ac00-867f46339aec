<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Pinjaman extends Model
{
    protected $table = 'pinjaman';
    public $timestamps = true;

    protected $fillable = [
        'user_id',
        'id_jenis_pinjaman',
        'bunga',
        'tanggal_pinjaman',
        'status',
    ];

    protected $casts = [
        'bunga' => 'decimal:2',
        'tanggal_pinjaman' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function jenisPinjaman(): BelongsTo
    {
        return $this->belongsTo(JenisPinjaman::class, 'id_jenis_pinjaman');
    }


    public function transaksiPinjaman(): HasMany
    {
        return $this->hasMany(TransaksiPinjaman::class, 'pinjaman_id');
    }
    
    // Keep this for backward compatibility during migration
    public function pembayaranPinjaman(): Has<PERSON><PERSON>
    {
        return $this->hasMany(PembayaranPinjaman::class, 'id_pinjaman');
    }
    public function getJumlahPinjamanAttribute($value)
    {
        $totalPencairan = $this->transaksiPinjaman()
            ->where('tipe_transaksi', 'pencairan')
            ->sum('jumlah');
        
        return $totalPencairan > 0 ? $totalPencairan : $value;
    }

    // Calculate total payments
    public function getTotalAngsuranAttribute()
    {
        return $this->transaksiPinjaman()
            ->where('tipe_transaksi', 'angsuran')
            ->sum('jumlah');
    }

    // Calculate remaining balance
    public function getSisaPinjamanAttribute()
    {
        return $this->jumlah_pinjaman - $this->total_angsuran;
    }
}
