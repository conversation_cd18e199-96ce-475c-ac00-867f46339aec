<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\SimpananNew as Simpanan;
use App\Models\PinjamanNew as Pi<PERSON>man;

class RiwayatController extends Controller
{
    public function index(Request $request)
    {
        $result = [];

        // Get query parameters with defaults
        $startDate = $request->input('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', now()->endOfMonth()->format('Y-m-d'));
        $type = $request->input('type', 'all');
        $sort = $request->input('sort', 'tanggal');
        $order = $request->input('order', 'desc');

        // 1. Simpanan (SimpananNew) for all users
        $simpananQuery = Simpanan::with('user')
            ->whereBetween('tanggal', [$startDate, $endDate]);
            
        // Add search filter if search term exists (case-insensitive)
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . strtolower($request->search) . '%';
            $simpananQuery->where(function($query) use ($searchTerm) {
                $query->whereHas('user', function($q) use ($searchTerm) {
                    $q->whereRaw('LOWER(name) LIKE ?', [$searchTerm]);
                })
                ->orWhereRaw('LOWER(keterangan) LIKE ?', [$searchTerm])
                ->orWhereRaw('CAST(jumlah AS TEXT) LIKE ?', [$searchTerm]);
            });
        }
        
        $simpananQuery = $simpananQuery->get();

        foreach ($simpananQuery as $s) {
            $result[] = [
                'id' => $s->id,
                'tanggal' => $s->tanggal,
                'jenis' => 'Simpanan',
                'tipe' => ucfirst(strtolower($s->tipe)),
                'kategori' => $s->jenis,
                'nama_jenis' => $s->jenis,
                'user_name' => $s->user->name ?? 'Unknown User',
                'anggota' => [
                    'nama' => $s->user->name ?? 'Unknown User',
                ],
                'nama_anggota' => $s->user->name ?? 'Unknown User',
                'jumlah' => $s->jumlah,
                'keterangan' => $s->keterangan,
                'tipe_transaksi' => $s->tipe,
                'status' => 'Berhasil',
            ];
        }

        // 2. Pinjaman (PinjamanNew) for all users
        $pinjamanQuery = Pinjaman::with('user')
            ->whereBetween('tanggal', [$startDate, $endDate]);
            
        // Add search filter if search term exists (case-insensitive)
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = '%' . strtolower($request->search) . '%';
            $pinjamanQuery->where(function($query) use ($searchTerm) {
                $query->whereHas('user', function($q) use ($searchTerm) {
                    $q->whereRaw('LOWER(name) LIKE ?', [$searchTerm]);
                })
                ->orWhereRaw('LOWER(keterangan) LIKE ?', [$searchTerm])
                ->orWhereRaw('CAST(jumlah AS TEXT) LIKE ?', [$searchTerm]);
            });
        }
        
        $pinjamanQuery = $pinjamanQuery->get();

        foreach ($pinjamanQuery as $p) {
            $result[] = [
                'id' => $p->id,
                'tanggal' => $p->tanggal,
                'jenis' => 'Pinjaman',
                'tipe' => $p->tipe,
                'kategori' => $p->jenis,
                'nama_jenis' => $p->jenis,
                'user_name' => $p->user->name ?? 'Unknown User',
                'anggota' => [
                    'nama' => $p->user->name ?? 'Unknown User',
                ],
                'nama_anggota' => $p->user->name ?? 'Unknown User',
                'jumlah' => $p->jumlah,
                'keterangan' => $p->keterangan,
                'tipe_transaksi' => $p->tipe,
                'status' => 'Berhasil',
            ];
        }

        // Filter by type if specified
        if ($type !== 'all') {
            $result = array_filter($result, function($item) use ($type) {
                return strtolower($item['jenis']) === strtolower($type);
            });
        }

        // Sort results
        usort($result, function($a, $b) use ($sort, $order) {
            $valueA = $a[$sort] ?? '';
            $valueB = $b[$sort] ?? '';
            
            if ($valueA == $valueB) return 0;
            
            $compare = $valueA <=> $valueB;
            return $order === 'asc' ? $compare : -$compare;
        });

        // Manual pagination
        $perPage = 10;
        $currentPage = $request->input('page', 1);
        $currentItems = array_slice($result, ($currentPage - 1) * $perPage, $perPage);
        
        // Create paginator instance
        $transactions = new \Illuminate\Pagination\LengthAwarePaginator(
            $currentItems,
            count($result),
            $perPage,
            $currentPage,
            [
                'path' => \Illuminate\Support\Facades\Request::url(),
                'query' => $request->query(),
            ]
        );

        // Return as Inertia response
        return Inertia::render('Riwayat/Index', [
            'transactions' => $transactions,
            'filters' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'type' => $type,
                'sort' => $sort,
                'order' => $order,
            ],
        ]);
    }

    /**
     * Update the specified transaction.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $type
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $type, $id)
    {
        $validated = $request->validate([
            'tanggal' => 'required|date',
            'jumlah' => 'required|numeric|min:0',
            'keterangan' => 'nullable|string|max:255',
            'status' => 'required|string|in:Berhasil,Menunggu,Ditolak,Lunas',
        ]);

        try {
            DB::beginTransaction();

            if (strtolower($type) === 'simpanan') {
                $transaction = Simpanan::findOrFail($id);
                $transaction->update([
                    'tanggal' => $validated['tanggal'],
                    'jumlah' => $validated['jumlah'],
                    'keterangan' => $validated['keterangan'],
                    'status' => $validated['status'],
                ]);
            } else if (strtolower($type) === 'pinjaman') {
                $transaction = Pinjaman::findOrFail($id);
                $transaction->update([
                    'tanggal' => $validated['tanggal'],
                    'jumlah' => $validated['jumlah'],
                    'keterangan' => $validated['keterangan'],
                    'status' => $validated['status'],
                ]);
            }

            DB::commit();
            return redirect()->back()->with('success', 'Transaksi berhasil diperbarui');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Gagal memperbarui transaksi: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified transaction.
     *
     * @param  string  $type
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($type, $id)
    {
        try {
            DB::beginTransaction();

            if (strtolower($type) === 'simpanan') {
                $transaction = Simpanan::findOrFail($id);
            } else if (strtolower($type) === 'pinjaman') {
                $transaction = Pinjaman::findOrFail($id);
            } else {
                throw new \Exception('Jenis transaksi tidak valid');
            }
            
            $transaction->delete();

            DB::commit();
            
            return back()->with('success', 'Transaksi berhasil dihapus');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting transaction: ' . $e->getMessage());
            
            return back()->with('error', 'Gagal menghapus transaksi: ' . $e->getMessage());
        }
    }

    public function bulkDestroy(Request $request)
    {
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'string',
        ]);

        $simpananIds = [];
        $pinjamanIds = [];

        foreach ($validated['ids'] as $id) {
            $parts = explode('-', $id);
            if (count($parts) !== 2) continue;
            
            $type = $parts[0];
            $realId = $parts[1];

            if ($type === 'simpanan') {
                $simpananIds[] = $realId;
            } elseif ($type === 'pinjaman') {
                $pinjamanIds[] = $realId;
            }
        }

        try {
            DB::beginTransaction();

            if (!empty($simpananIds)) {
                Simpanan::whereIn('id', $simpananIds)->delete();
            }

            if (!empty($pinjamanIds)) {
                Pinjaman::whereIn('id', $pinjamanIds)->delete();
            }

            DB::commit();
            return back()->with('success', 'Transaksi terpilih berhasil dihapus');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error during bulk delete: ' . $e->getMessage());
            return back()->with('error', 'Gagal menghapus transaksi terpilih: ' . $e->getMessage());
        }
    }

    /**
     * Delete all transactions.
     *
     * @return \Illuminate\Http\Response
     */
    public function deleteAll()
    {
        try {
            DB::beginTransaction();

            // Delete all simpanan records
            $simpananCount = Simpanan::count();
            Simpanan::truncate();

            // Delete all pinjaman records
            $pinjamanCount = Pinjaman::count();
            Pinjaman::truncate();

            $totalDeleted = $simpananCount + $pinjamanCount;
            
            DB::commit();
            
            return response()->json([
                'message' => 'Semua transaksi berhasil dihapus',
                'count' => $totalDeleted
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting all transactions: ' . $e->getMessage());
            return response()->json([
                'message' => 'Gagal menghapus semua transaksi: ' . $e->getMessage()
            ], 500);
        }
    }
}
