<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Inertia\Inertia;

class LaporanController extends Controller
{
    public function index(Request $request)
    {
        $startDate = $request->input('start_date') 
            ? Carbon::parse($request->input('start_date')) 
            : now()->startOfMonth();
            
        $endDate = $request->input('end_date') 
            ? Carbon::parse($request->input('end_date'))
            : now()->endOfMonth();
            
        $search = $request->input('search');

        // Get all transactions between the date range
        $transactions = $this->getTransactions($startDate, $endDate, $search);

        return Inertia::render('Laporan/Index', [
            'laporan' => $transactions,
            'filters' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'search' => $search ?? '',
            ]
        ]);
    }

    private function getTransactions($startDate, $endDate, $search = null)
    {
        // Return empty collection if no search term is provided
        if (empty($search)) {
            return [
                'member' => null,
                'transactions' => []
            ];
        }
        
        // Get member information
        $member = DB::table('users')
            ->where('role', 'anggota')
            ->where(function($query) use ($search) {
                $query->where('name', 'ilike', "%{$search}%")
                      ->orWhere('nik', 'ilike', "%{$search}%");
            })
            ->select('id', 'name', 'nik', 'bagian')
            ->first();
            
        if (!$member) {
            return [
                'member' => null,
                'transactions' => []
            ];
        }

        // Format dates for query
        $start = $startDate->format('Y-m-d');
        $end = $endDate->format('Y-m-d');

        // Base query for savings transactions
        $savingsQuery = DB::table('transaksi_simpanan')
            ->join('simpanan', 'transaksi_simpanan.simpanan_id', '=', 'simpanan.id')
            ->join('jenis_simpanan', 'simpanan.id_jenis_simpanan', '=', 'jenis_simpanan.id_jenis_simpanan')
            ->join('users', 'simpanan.user_id', '=', 'users.id')
            ->select(
                'transaksi_simpanan.tanggal_transaksi as tanggal',
                'jenis_simpanan.nama_jenis',
                'transaksi_simpanan.tipe_transaksi',
                'transaksi_simpanan.jumlah',
                'users.name as user_name',
                'users.nik as user_nik',
                DB::raw("'' as keterangan"),
                DB::raw("CASE 
                    WHEN jenis_simpanan.nama_jenis = 'Pokok' AND tipe_transaksi = 'Setoran' THEN 'Setoran Simpanan Pokok'
                    WHEN jenis_simpanan.nama_jenis = 'Wajib' AND tipe_transaksi = 'Setoran' THEN 'Setoran Simpanan Wajib'
                    WHEN jenis_simpanan.nama_jenis = 'Sukarela' AND tipe_transaksi = 'Setoran' THEN 'Setoran Simpanan Sukarela'
                    WHEN jenis_simpanan.nama_jenis = 'Sukarela' AND tipe_transaksi = 'Pengambilan' THEN 'Penarikan Simpanan Sukarela'
                    ELSE CONCAT(tipe_transaksi, ' ', jenis_simpanan.nama_jenis)
                END as keterangan_detil")
            )
            ->whereBetween('transaksi_simpanan.tanggal_transaksi', [$start, $end]);
            
        // Add search filter if provided
        if ($search) {
            $savingsQuery->where(function($query) use ($search) {
                $query->where('users.name', 'ilike', "%{$search}%")
                      ->orWhere('users.nik', 'ilike', "%{$search}%");
            });
        }
        
        $savings = $savingsQuery->where('users.id', $member->id)->get();

        // Query for all loan transactions using Eloquent models
        $loans = \App\Models\TransaksiPinjaman::with(['pinjaman.jenisPinjaman', 'pinjaman.user'])
            ->whereIn('tipe_transaksi', ['pencairan', 'angsuran', 'denda', 'lainnya'])
            ->whereBetween('tanggal_transaksi', [$start, $end])
            ->whereHas('pinjaman.user', function($query) use ($member) {
                $query->where('id', $member->id);
            })
            ->when($search, function($query) use ($search) {
                $query->whereHas('pinjaman.user', function($q) use ($search) {
                    $q->where('name', 'ilike', "%{$search}%")
                      ->orWhere('nik', 'ilike', "%{$search}%");
                });
            })
            ->orderBy('tanggal_transaksi')
            ->get()
            ->map(function($transaction) {
                $isDisbursement = $transaction->tipe_transaksi === 'pencairan';
                $isJasaPinjaman = $transaction->tipe_transaksi === 'lainnya' && 
                                 str_contains($transaction->keterangan ?? '', 'Jasa Pinjaman');
                $isInterest = $transaction->tipe_transaksi === 'denda' || $isJasaPinjaman;
                
                $pinjaman = $transaction->pinjaman;
                $jenisPinjaman = $pinjaman->jenisPinjaman;
                $user = $pinjaman->user;
                
                // For Jasa Pinjaman, use the transaction amount directly
                if ($isJasaPinjaman) {
                    return (object) [
                        'tanggal' => $transaction->tanggal_transaksi,
                        'nama_jenis' => $jenisPinjaman->nama_jenis ?? 'Umum',
                        'jumlah_pokok' => 0, // Jasa Pinjaman doesn't affect principal
                        'user_name' => $user->name,
                        'user_nik' => $user->nik,
                        'jumlah_bunga' => $transaction->jumlah, // This will be shown as interest
                        'keterangan' => $transaction->keterangan,
                        'keterangan_detil' => $transaction->keterangan,
                        'is_disbursement' => false,
                        'is_interest' => true
                    ];
                }
                
                // For regular interest payments
                $jumlahBunga = $isInterest ? $transaction->jumlah : 0;
                
                // For installments, calculate interest based on previous transactions
                if ($transaction->tipe_transaksi === 'angsuran') {
                    $previousTransaction = \App\Models\TransaksiPinjaman::where('pinjaman_id', $pinjaman->id)
                        ->where('id_transaksi', '<', $transaction->id_transaksi)
                        ->whereIn('tipe_transaksi', ['angsuran', 'denda'])
                        ->orderBy('tanggal_transaksi', 'desc')
                        ->first();
                    
                    if ($previousTransaction) {
                        $days = $transaction->tanggal_transaksi->diffInDays($previousTransaction->tanggal_transaksi);
                        $jumlahBunga = $pinjaman->jumlah_pinjaman * ($pinjaman->bunga / 100) * ($days / 30);
                    }
                }
                
                return (object) [
                    'tanggal' => $transaction->tanggal_transaksi,
                    'nama_jenis' => $jenisPinjaman->nama_jenis ?? 'Umum',
                    'jumlah_pokok' => $isDisbursement ? $transaction->jumlah : -$transaction->jumlah,
                    'user_name' => $user->name,
                    'user_nik' => $user->nik,
                    'jumlah_bunga' => $isInterest ? -$transaction->jumlah : $jumlahBunga,
                    'keterangan' => $transaction->keterangan,
                    'keterangan_detil' => $isInterest 
                        ? 'Bunga Pinjaman ' . ($jenisPinjaman->nama_jenis ?? 'Umum') 
                        : $this->getKeteranganDetil($transaction->tipe_transaksi, $jenisPinjaman->nama_jenis ?? 'Umum'),
                    'is_disbursement' => $isDisbursement,
                    'is_interest' => $isInterest
                ];
            });

        // Combine and format all transactions
        $transactions = collect([]);
        
        // Process savings
        foreach ($savings as $saving) {
            $transaction = [
                'tanggal' => $saving->tanggal,
                'keterangan' => $saving->keterangan_detil,
                'simpanan_pokok' => 0,
                'simpanan_wajib' => 0,
                'simpanan_sukarela' => 0,
                'simpanan_sihara' => 0,
                'simpanan_deposito' => 0,
                'pinjaman_umum' => 0,
                'pinjaman_khusus' => 0,
                'pinjaman_emg' => 0,
                'bunga_umum' => 0,
                'bunga_khusus' => 0,
                'bunga_emg' => 0,
                'saldo' => 0 // Will be calculated later
            ];

            // Map transaction to correct column based on type
            $amount = $saving->tipe_transaksi === 'Setoran' ? $saving->jumlah : -$saving->jumlah;
            
            switch($saving->nama_jenis) {
                case 'Pokok':
                    $transaction['simpanan_pokok'] = $amount;
                    break;
                case 'Wajib':
                    $transaction['simpanan_wajib'] = $amount;
                    break;
                case 'Sukarela':
                    $transaction['simpanan_sukarela'] = $amount;
                    break;
                case 'Sihara':
                    $transaction['simpanan_sihara'] = $amount;
                    break;
                case 'Deposito':
                    $transaction['simpanan_deposito'] = $amount;
                    break;
            }

            $transactions->push($transaction);
        }

        // Group loans by date and type to combine JSP with disbursements
        $groupedLoans = [];
        
        // First, group loans by date and type
        foreach ($loans as $loan) {
            $key = $loan->tanggal . '_' . strtolower($loan->nama_jenis);
            
            if (!isset($groupedLoans[$key])) {
                $groupedLoans[$key] = [
                    'disbursement' => null,
                    'jsp' => null,
                    'tanggal' => $loan->tanggal,
                    'nama_jenis' => $loan->nama_jenis,
                    'keterangan_detil' => $loan->keterangan_detil
                ];
            }
            
            if ($loan->is_disbursement) {
                $groupedLoans[$key]['disbursement'] = $loan;
                $groupedLoans[$key]['keterangan_detil'] = $loan->keterangan_detil; // Use disbursement description
            } elseif ($loan->is_interest) {
                $groupedLoans[$key]['jsp'] = $loan;
            }
        }
        
        // Process grouped loans
        foreach ($groupedLoans as $group) {
            $transaction = [
                'tanggal' => $group['tanggal'],
                'keterangan' => $group['keterangan_detil'],
                'simpanan_pokok' => 0,
                'simpanan_wajib' => 0,
                'simpanan_sukarela' => 0,
                'simpanan_sihara' => 0,
                'simpanan_deposito' => 0,
                'pinjaman_umum' => 0,
                'pinjaman_khusus' => 0,
                'pinjaman_emg' => 0,
                'bunga_umum' => 0,
                'bunga_khusus' => 0,
                'bunga_emg' => 0,
                'saldo' => 0
            ];
            
            $loanType = strtolower($group['nama_jenis']);
            $disbursement = $group['disbursement'];
            $jsp = $group['jsp'];
            
            // Handle disbursement if exists
            if ($disbursement) {
                $amount = abs($disbursement->jumlah_pokok);
                
                switch($loanType) {
                    case 'umum':
                        $transaction['pinjaman_umum'] = $amount;
                        break;
                    case 'khusus':
                        $transaction['pinjaman_khusus'] = $amount;
                        break;
                    case 'emergency':
                        $transaction['pinjaman_emg'] = $amount;
                        break;
                    default:
                        $transaction['pinjaman_umum'] = $amount;
                        break;
                }
                
                // Update description to include JSP info if available
                if ($jsp) {
                    $transaction['keterangan'] = $disbursement->keterangan_detil . ' + ' . $jsp->keterangan;
                }
            }
            
            // Handle JSP if exists
            if ($jsp) {
                $jspAmount = -abs($jsp->jumlah_bunga);
                
                switch($loanType) {
                    case 'umum':
                        $transaction['bunga_umum'] = $jspAmount;
                        break;
                    case 'khusus':
                        $transaction['bunga_khusus'] = $jspAmount;
                        break;
                    case 'emergency':
                        $transaction['bunga_emg'] = $jspAmount;
                        break;
                    default:
                        $transaction['bunga_umum'] = $jspAmount;
                        break;
                }
                
                // If no disbursement, use JSP description
                if (!$disbursement) {
                    $transaction['keterangan'] = $jsp->keterangan;
                }
            }
            
            // Only add transaction if there's actual data
            if ($disbursement || $jsp) {
                $transactions->push($transaction);
            }
        }

        // Sort transactions by date
        $sortedTransactions = $transactions->sortBy('tanggal');

        // Calculate running balance for each transaction starting from 0
        $runningBalance = 0;
        $formattedTransactions = $sortedTransactions->map(function ($transaction) use (&$runningBalance) {
            // Ensure all required keys exist
            $transaction = array_merge([
                'simpanan_pokok' => 0,
                'simpanan_wajib' => 0,
                'simpanan_sukarela' => 0,
                'simpanan_sihara' => 0,
                'simpanan_deposito' => 0,
                'pinjaman_umum' => 0,
                'pinjaman_khusus' => 0,
                'pinjaman_emg' => 0,
                'bunga_umum' => 0,
                'bunga_khusus' => 0,
                'bunga_emg' => 0
            ], $transaction);
            
            // Calculate the net effect of this transaction
            // Savings and deposits add to balance
            $netEffect = 
                $transaction['simpanan_pokok'] + 
                $transaction['simpanan_wajib'] + 
                $transaction['simpanan_sukarela'] +
                $transaction['simpanan_sihara'] +
                $transaction['simpanan_deposito'];
            
            // Loan disbursements add to balance (positive), payments reduce it (negative)
            $netEffect += 
                $transaction['pinjaman_umum'] + 
                $transaction['pinjaman_khusus'] + 
                $transaction['pinjaman_emg'];
            
            // Interest payments reduce the balance (already negative in the transaction)
            $netEffect +=
                $transaction['bunga_umum'] + 
                $transaction['bunga_khusus'] + 
                $transaction['bunga_emg'];
            
            // Update running balance
            $runningBalance += $netEffect;
            $transaction['saldo'] = $runningBalance;
            
            return $transaction;
        });

        return [
            'member' => $member,
            'transactions' => array_values($formattedTransactions->toArray())
        ];
    }

    public function searchMembers(Request $request)
    {
        $search = $request->input('search');
        
        if (empty($search)) {
            return response()->json([]);
        }
        
        $members = DB::table('users')
            ->where('role', 'anggota')
            ->where(function($query) use ($search) {
                $query->where('name', 'ilike', "%{$search}%")
                      ->orWhere('nik', 'ilike', "%{$search}%");
            })
            ->select('id', 'name', 'nik', 'bagian')
            ->limit(10)
            ->get();
            
        return response()->json($members);
    }
    
    private function getStartingBalance($startDate)
    {
        // Get total savings before start date using Eloquent
        $totalDeposit = \App\Models\TransaksiSimpanan::where('tipe_transaksi', 'Setoran')
            ->where('tanggal_transaksi', '<', $startDate)
            ->sum('jumlah');
            
        $totalWithdrawal = \App\Models\TransaksiSimpanan::where('tipe_transaksi', 'Pengambilan')
            ->where('tanggal_transaksi', '<', $startDate)
            ->sum('jumlah');
            
        $totalSavings = $totalDeposit - $totalWithdrawal;

        // Get total loan payments before start date using Eloquent
        $totalLoanPayments = \App\Models\TransaksiPinjaman::where('tipe_transaksi', 'angsuran')
            ->where('tanggal_transaksi', '<', $startDate)
            ->sum('jumlah');

        // Starting balance = (total savings) - (total loan payments)
        return $totalSavings - $totalLoanPayments;
    }
    
    /**
     * Generate keterangan_detil based on transaction type and loan type
     */
    private function getKeteranganDetil($tipeTransaksi, $jenisPinjaman)
    {
        switch ($tipeTransaksi) {
            case 'pencairan':
                return "Pencairan Pinjaman " . $jenisPinjaman;
            case 'angsuran':
                return "Angsuran Pinjaman " . $jenisPinjaman;
            default:
                return ucfirst($tipeTransaksi) . ' ' . $jenisPinjaman;
        }
    }
}
