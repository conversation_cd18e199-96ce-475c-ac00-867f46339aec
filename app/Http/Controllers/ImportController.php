<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx as XlsxReader;
use PhpOffice\PhpSpreadsheet\Reader\Csv as CsvReader;

class ImportController extends Controller
{
    /**
     * Download import template for the specified type
     *
     * @param string $type
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadTemplate($type)
    {
        $allowedTypes = ['simpanan', 'pinjaman'];
        
        if (!in_array($type, $allowedTypes)) {
            abort(404, 'Template tidak tersedia');
        }

        $exportClass = 'App\\Exports\\' . ucfirst($type) . 'TemplateExport';
        
        if (!class_exists($exportClass)) {
            abort(500, 'Kelas ekspor tidak ditemukan');
        }

        $exporter = new $exportClass();
        
        return $exporter->download();
    }

    /**
     * Handle import data from Excel file
     *
     * @param Request $request
     * @param string $type
     * @return \Illuminate\Http\JsonResponse
     */
    public function import(Request $request, $type)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv'
        ]);

        $allowedTypes = ['simpanan', 'pinjaman'];
        
        if (!in_array($type, $allowedTypes)) {
            return response()->json([
                'message' => 'Tipe import tidak valid',
                'errors' => [
                    'type' => ['Tipe import tidak didukung']
                ]
            ], 422);
        }

        try {
            $importClass = 'App\\Imports\\' . ucfirst($type) . 'Import';
            $filename = $request->file('file')->getClientOriginalName();
            $import = new $importClass($filename);
            
            $extension = $request->file('file')->getClientOriginalExtension();
            
            if ($extension === 'csv') {
                $reader = new CsvReader();
            } else {
                $reader = new XlsxReader();
            }
            
            $spreadsheet = $reader->load($request->file('file')->getPathname());
            
            $result = $import->process($spreadsheet->getActiveSheet());
            
            if ($result['success']) {
                return response()->json([
                    'message' => 'Data berhasil diimport',
                    'imported' => $result['imported'],
                    'errors' => $result['errors']
                ]);
            } else {
                return response()->json([
                    'message' => $result['message'] ?? 'Terjadi kesalahan saat mengimport data',
                    'errors' => $result['errors'] ?? []
                ], 422);
            }
            
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Terjadi kesalahan saat memproses file',
                'error' => $e->getMessage(),
                'trace' => config('app.debug') ? $e->getTraceAsString() : null
            ], 500);
        }
    }

    /**
     * Get importable types
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getImportableTypes()
    {
        return response()->json([
            'data' => [
                [
                    'id' => 'simpanan',
                    'name' => 'Simpanan',
                    'description' => 'Import data simpanan anggota',
                    'template_url' => route('import.template', ['type' => 'simpanan'])
                ],
                [
                    'id' => 'pinjaman',
                    'name' => 'Pinjaman',
                    'description' => 'Import data pinjaman anggota',
                    'template_url' => route('import.template', ['type' => 'pinjaman'])
                ]
            ]
        ]);
    }
}
