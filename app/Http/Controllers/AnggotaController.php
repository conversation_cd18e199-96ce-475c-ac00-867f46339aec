<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;

class AnggotaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (auth()->user()->role !== 'admin') {
            return redirect()->route('member.dashboard');
        }

        $perPage = request()->input('per_page', 10);
        $status = request()->input('status');
        $search = request()->input('search');
        
        $query = User::where('role', 'anggota')
                    ->when($status && $status !== 'all', function($q) use ($status) {
                        $statusValue = $status === 'Tidak Aktif' ? 'Non-Aktif' : $status;
                        return $q->where('status', $statusValue);
                    })
                    ->when($search, function($q) use ($search) {
                        return $q->where(function($query) use ($search) {
                            $query->where('nik', 'like', "%{$search}%")
                                  ->orWhereRaw('LOWER(name) LIKE ?', ["%" . strtolower($search) . "%"])
                                  ->orWhere('email', 'like', "%{$search}%");
                        });
                    })
                    ->orderBy('name', 'asc')
                    ->orderBy('id', 'asc');

        $users = $query->paginate($perPage)
                     ->appends(request()->query());

        return Inertia::render('Anggota/Index', [
            'users' => $users,
            'filters' => request()->only(['status', 'search'])
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (auth()->user()->role !== 'admin') {
            return redirect()->route('member.dashboard');
        }
        return Inertia::render('Anggota/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (auth()->user()->role !== 'admin') {
            return redirect()->route('member.dashboard');
        }
        $validated = $request->validate([
            'nik' => 'required|string|max:20|unique:users,nik',
            'nama' => 'required|string|max:255',
            'email' => 'nullable|string|email|max:255|unique:users',
            'bagian' => 'required|string|max:100',  
        ]);

        $validated['name'] = $validated['nama'];
        $validated['password'] = bcrypt('password123');
        $validated['role'] = 'anggota';
        $validated['status'] = 'Aktif';
        $validated['tanggal_masuk'] = now();

        User::create($validated);

        return redirect()->route('admin.anggota.index')
            ->with('success', 'Data anggota berhasil ditambahkan');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        if (auth()->user()->role !== 'admin') {
            return redirect()->route('member.dashboard');
        }
        $user = User::findOrFail($id);
        return Inertia::render('Anggota/Show', [
            'user' => $user
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        if (auth()->user()->role !== 'admin') {
            return redirect()->route('member.dashboard');
        }
        $user = User::findOrFail($id);
        return Inertia::render('Anggota/Edit', [
            'user' => $user
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        if (auth()->user()->role !== 'admin') {
            return redirect()->route('member.dashboard');
        }
        $user = User::findOrFail($id);

        $validated = $request->validate([
            'nik' => 'required|numeric|unique:users,nik,' . $user->id,
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'bagian' => 'required|string|max:100',
            'tanggal_masuk' => 'required|date',
            'tanggal_keluar' => 'nullable|date|after_or_equal:tanggal_masuk',
            'alasan_keluar' => 'nullable|string|max:255',
            'status' => 'required|in:Aktif,Non-Aktif',
        ]);

        if ($validated['status'] === 'Non-Aktif' && empty($validated['tanggal_keluar'])) {
            $validated['tanggal_keluar'] = now();
        }

        $user->update($validated);

        return redirect()->route('admin.anggota.index')
            ->with('success', 'Data anggota berhasil diperbarui');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        if (auth()->user()->role !== 'admin') {
            return redirect()->route('member.dashboard');
        }
        $user = User::findOrFail($id);
        
        if ($user->simpanan()->exists() || $user->pinjaman()->exists()) {
            return back()->with('error', 'Tidak dapat menghapus anggota karena memiliki data terkait');
        }

        $user->delete();

        return redirect()->route('admin.anggota.index')
            ->with('success', 'Data anggota berhasil dihapus');
    }
}
