<?php

namespace App\Http\Controllers;

use App\Models\Simpanan;
use App\Models\TransaksiSimpanan;
use App\Models\JenisSimpanan;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class SimpananController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $month = $request->input('month', date('Y-m'));
        
        $date = Carbon::createFromFormat('Y-m', $month);
        $startOfMonth = $date->copy()->startOfMonth();
        $endOfMonth = $date->copy()->endOfMonth();

        $query = User::select('users.*')
            ->join('simpanan', 'users.id', '=', 'simpanan.user_id')
            ->groupBy('users.id', 'users.name', 'users.nik', 'users.bagian')
            ->orderBy('users.name');

        $users = $query->paginate($perPage);

        $formattedData = $users->getCollection()->map(function($user) use ($startOfMonth, $endOfMonth) {
            $simpananUser = Simpanan::with('jenisSimpanan')
                ->where('user_id', $user->id)
                ->get();
                
            $transaksiBulanIni = \App\Models\TransaksiSimpanan::whereIn('simpanan_id', $simpananUser->pluck('id'))
                ->whereBetween('tanggal_transaksi', [$startOfMonth, $endOfMonth])
                ->get()
                ->groupBy('simpanan_id');
                
            $simpananUser->each(function($simpanan) use ($transaksiBulanIni) {
                $simpanan->setRelation('transaksiSimpanan', $transaksiBulanIni->get($simpanan->id, collect()));
            });

            $jenisTransaksi = [
                'setoran_pokok' => 0,
                'setoran_wajib' => 0,
                'setoran_sukarela' => 0,
                'setoran_sihara' => 0,
                'setoran_deposito' => 0,
                'penarikan_pokok' => 0,
                'penarikan_wajib' => 0,
                'penarikan_sukarela' => 0,
                'penarikan_sihara' => 0,
                'penarikan_deposito' => 0,
            ];

            $saldoAwal = 0;
            $totalPokok = 0;
            $totalWajib = 0;
            $totalSukarela = 0;
            $totalSihara = 0;
            $totalDeposito = 0;
            
            $transaksiBulanIni = [
                'Pokok' => ['setoran' => 0, 'penarikan' => 0],
                'Wajib' => ['setoran' => 0, 'penarikan' => 0],
                'Sukarela' => ['setoran' => 0, 'penarikan' => 0],
                'Sihara' => ['setoran' => 0, 'penarikan' => 0],
                'Deposito' => ['setoran' => 0, 'penarikan' => 0],
            ];

            // First, calculate saldo awal from all transactions before current month
            foreach ($simpananUser as $simpanan) {
                $jenis = $simpanan->jenisSimpanan->nama_jenis ?? '';
                
                // Calculate saldo awal for each type up to the end of previous month
                $saldoAwalJenis = $simpanan->transaksiSimpanan()
                    ->where('tanggal_transaksi', '<', $startOfMonth)
                    ->sum(DB::raw("CASE 
                        WHEN tipe_transaksi = 'Setoran' THEN jumlah 
                        WHEN tipe_transaksi = 'Pengambilan' THEN -jumlah 
                        ELSE 0 
                    END"));
                
                // Store the initial balance for this type
                ${'saldoAwal' . $jenis} = $saldoAwalJenis;
                $saldoAwal += $saldoAwalJenis;
            }

            // Then process current month's transactions
            foreach ($simpananUser as $simpanan) {
                $jenis = $simpanan->jenisSimpanan->nama_jenis ?? '';
                
                // Get current month transactions
                $transaksiBulanIniSet = $simpanan->transaksiSimpanan()
                    ->whereBetween('tanggal_transaksi', [$startOfMonth, $endOfMonth])
                    ->where('tipe_transaksi', 'Setoran')
                    ->sum('jumlah');
                    
                $transaksiBulanIniPen = $simpanan->transaksiSimpanan()
                    ->whereBetween('tanggal_transaksi', [$startOfMonth, $endOfMonth])
                    ->where('tipe_transaksi', 'Pengambilan')
                    ->sum('jumlah');
                
                // Ensure we're using the correct jenis name (case-sensitive)
                $jenisKey = ucfirst(strtolower($jenis));
                if (!isset($transaksiBulanIni[$jenisKey])) {
                    $transaksiBulanIni[$jenisKey] = ['setoran' => 0, 'penarikan' => 0];
                }
                
                $transaksiBulanIni[$jenisKey]['setoran'] += $transaksiBulanIniSet;
                $transaksiBulanIni[$jenisKey]['penarikan'] += $transaksiBulanIniPen;
                
                // Calculate current balance for this type
                $saldoJenis = ${'saldoAwal' . $jenis} + $transaksiBulanIniSet - $transaksiBulanIniPen;
                
                // Store the balance for this type
                if ($jenis === 'Pokok') {
                    $totalPokok = $saldoJenis;
                } elseif ($jenis === 'Wajib') {
                    $totalWajib = $saldoJenis;
                } elseif ($jenis === 'Sukarela') {
                    $totalSukarela = $saldoJenis;
                } elseif ($jenis === 'Sihara') {
                    $totalSihara = $saldoJenis;
                } elseif ($jenis === 'Deposito') {
                    $totalDeposito = $saldoJenis;
                }
                
                $jenisTransaksi['setoran_' . strtolower($jenis)] = $transaksiBulanIniSet;
                $jenisTransaksi['penarikan_' . strtolower($jenis)] = $transaksiBulanIniPen;
            }

            // Debug: Log the transaction data
            Log::info('Transaction Data:', [
                'transaksiBulanIni' => $transaksiBulanIni,
                'totalPokok' => $totalPokok,
                'totalWajib' => $totalWajib,
                'totalSukarela' => $totalSukarela,
                'totalSihara' => $totalSihara,
                'totalDeposito' => $totalDeposito
            ]);

            // Calculate total from all transactions (deposits - withdrawals)
            $totalPenjamin = (float)$totalPokok + (float)$totalWajib + (float)$totalSukarela;
            $totalSemua = (float)$totalPokok + (float)$totalWajib + (float)$totalSukarela + (float)$totalSihara + (float)$totalDeposito;
            
            // Get transaction amounts for the current month
            $setoranPokok = isset($transaksiBulanIni['Pokok']) ? $transaksiBulanIni['Pokok']['setoran'] : 0;
            $penarikanPokok = isset($transaksiBulanIni['Pokok']) ? $transaksiBulanIni['Pokok']['penarikan'] : 0;
            $setoranWajib = isset($transaksiBulanIni['Wajib']) ? $transaksiBulanIni['Wajib']['setoran'] : 0;
            $penarikanWajib = isset($transaksiBulanIni['Wajib']) ? $transaksiBulanIni['Wajib']['penarikan'] : 0;
            $setoranSukarela = isset($transaksiBulanIni['Sukarela']) ? $transaksiBulanIni['Sukarela']['setoran'] : 0;
            $penarikanSukarela = isset($transaksiBulanIni['Sukarela']) ? $transaksiBulanIni['Sukarela']['penarikan'] : 0;
            $setoranSihara = isset($transaksiBulanIni['Sihara']) ? $transaksiBulanIni['Sihara']['setoran'] : 0;
            $penarikanSihara = isset($transaksiBulanIni['Sihara']) ? $transaksiBulanIni['Sihara']['penarikan'] : 0;
            $setoranDeposito = isset($transaksiBulanIni['Deposito']) ? $transaksiBulanIni['Deposito']['setoran'] : 0;
            $penarikanDeposito = isset($transaksiBulanIni['Deposito']) ? $transaksiBulanIni['Deposito']['penarikan'] : 0;
            
            // Log for debugging
            \Log::info('Perhitungan Simpanan', [
                'user_id' => $user->id,
                'saldo_awal' => $saldoAwal,
                'transaksi_bulan_ini' => $transaksiBulanIni,
                'setoran_pokok' => $setoranPokok,
                'penarikan_pokok' => $penarikanPokok,
                'total_penjamin' => $totalPenjamin,
                'total_semua' => $totalSemua
            ]);

            return [
                'id' => $user->id,
                'nik' => $user->nik,
                'name' => $user->name,
                'bagian' => $user->bagian,
                'saldo_awal' => $saldoAwal,
                'last_update' => now(),
                // Current balances
                'pokok' => $totalPokok,
                'wajib' => $totalWajib,
                'sukarela' => $totalSukarela,
                'sihara' => $totalSihara,
                'deposito' => $totalDeposito,
                // Deposits
                'setoran_pokok' => $setoranPokok,
                'setoran_wajib' => $setoranWajib,
                'setoran_sukarela' => $setoranSukarela,
                'setoran_sihara' => $setoranSihara,
                'setoran_deposito' => $setoranDeposito,
                // Withdrawals
                'penarikan_pokok' => $penarikanPokok,
                'penarikan_wajib' => $penarikanWajib,
                'penarikan_sukarela' => $penarikanSukarela,
                'penarikan_sihara' => $penarikanSihara,
                'penarikan_deposito' => $penarikanDeposito,
                'total_penjamin' => $totalPenjamin,
                'total_semua' => $totalSemua,
            ];
        });

        $users->setCollection($formattedData);

        return Inertia::render('Simpanan/Index', [
            'simpanan' => $users,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'nik' => 'required|string|exists:users,nik',
            'jenis_simpanan' => 'required|in:Pokok,Wajib,Sukarela,Sihara,Deposito',
            'tipe_transaksi' => 'required|in:Setoran,Pengambilan',
            'jumlah' => 'required|numeric|min:1000',
            'keterangan' => 'nullable|string|max:255',
        ]);

        try {
            DB::beginTransaction();

            // Debug: Check the values we're searching for
            \Log::info('Searching for simpanan with:', [
                'nik' => $request->nik,
                'jenis_simpanan' => $request->jenis_simpanan
            ]);

            // First, find the jenis_simpanan_id
            $jenisSimpanan = \DB::table('jenis_simpanan')
                ->where('nama_jenis', $request->jenis_simpanan)
                ->first();

            if (!$jenisSimpanan) {
                throw new \Exception('Jenis simpanan tidak ditemukan: ' . $request->jenis_simpanan);
            }

            // Debug: Log the jenis_simpanan_id
            \Log::info('Found jenis_simpanan_id:', ['id' => $jenisSimpanan->id_jenis_simpanan]);

            // Find the user's simpanan record
            $simpanan = Simpanan::whereHas('user', function($q) use ($request) {
                    $q->where('nik', $request->nik);
                })
                ->where('id_jenis_simpanan', $jenisSimpanan->id_jenis_simpanan)
                ->first();

            if (!$simpanan) {
                // If simpanan doesn't exist, create it
                $user = \App\Models\User::where('nik', $request->nik)->firstOrFail();
                
                $simpanan = new Simpanan([
                    'user_id' => $user->id,
                    'id_jenis_simpanan' => $jenisSimpanan->id_jenis_simpanan,
                    'tanggal_dibuat' => now(),
                ]);
                $simpanan->save();
                
                \Log::info('Created new simpanan record:', ['id' => $simpanan->id]);
            }

            // Create the transaction
            $transaksi = new TransaksiSimpanan([
                'simpanan_id' => $simpanan->id,
                'tipe_transaksi' => $request->tipe_transaksi,
                'jumlah' => $request->jumlah,
                'keterangan' => $request->keterangan,
                'tanggal_transaksi' => now(),
            ]);

            $transaksi->save();

            // Calculate new saldo based on all transactions
            $newSaldo = $simpanan->calculateSaldo();
            
            // For withdrawals, check if there's enough balance
            if ($request->tipe_transaksi === 'Pengambilan' && $newSaldo < 0) {
                DB::rollBack();
                throw new \Exception('Saldo tidak mencukupi');
            }

            DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'message' => 'Transaksi berhasil disimpan',
                    'transaction' => $transaksi
                ]);
            }

            return redirect()->back()->with('success', 'Transaksi berhasil disimpan');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'message' => 'Gagal menyimpan transaksi: ' . $e->getMessage()
                ], 422);
            }

            return back()->withErrors(['error' => 'Gagal menyimpan transaksi: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
    
    /**
     * Get active savings for a user
     *
     * @param int $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getActiveSavings($userId)
    {
        try {
            Log::info('Fetching active savings for user ID: ' . $userId);
            
            $savings = Simpanan::with(['jenisSimpanan', 'transaksiSimpanan' => function($query) {
                $query->orderBy('tanggal_transaksi', 'desc')->take(1);
            }])
            ->where('user_id', $userId)
            ->get()
            ->map(function($saving) {
                $lastTransaction = $saving->transaksiSimpanan->first();
                $saldo = $saving->calculateSaldo();
                
                return [
                    'id' => $saving->id,
                    'jenis_simpanan' => $saving->jenisSimpanan ? $saving->jenisSimpanan->nama_jenis : 'Unknown',
                    'saldo' => $saldo,
                    'tanggal_terakhir_transaksi' => $lastTransaction ? 
                        Carbon::parse($lastTransaction->tanggal_transaksi)->format('Y-m-d') : 
                        'Belum ada transaksi'
                ];
            });

            Log::info('Active savings found: ' . $savings->count());
            return response()->json($savings);
            
        } catch (\Exception $e) {
            Log::error('Error fetching active savings: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return response()->json([
                'error' => 'Failed to fetch savings data',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
