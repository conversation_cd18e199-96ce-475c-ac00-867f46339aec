<?php

namespace App\Http\Controllers\Member;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use App\Models\SimpananNew as Simpanan;
use App\Models\PinjamanNew as Pinjaman;

class RiwayatTransaksiController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        $result = [];

        // Get query parameters with defaults
        // Handle both start_date/end_date and bulan/tahun parameters
        if ($request->has('bulan') && $request->has('tahun')) {
            $bulan = (int) $request->input('bulan');
            $tahun = (int) $request->input('tahun');

            // Simple date calculation without Carbon
            $startDate = sprintf('%04d-%02d-01', $tahun, $bulan);
            $endDate = date('Y-m-t', mktime(0, 0, 0, $bulan, 1, $tahun));
        } else {
            $startDate = $request->input('start_date', now()->startOfMonth()->format('Y-m-d'));
            $endDate = $request->input('end_date', now()->endOfMonth()->format('Y-m-d'));
        }


        $type = $request->input('type', 'all');
        $sort = $request->input('sort', 'tanggal');
        $order = $request->input('order', 'desc');

        // 1. Simpanan (Transaksi Simpanan)
        $simpananTransactions = Simpanan::where('anggota_id', $user->id)
            ->whereDate('tanggal', '>=', $startDate)
            ->whereDate('tanggal', '<=', $endDate)
            ->orderBy('tanggal', 'desc')
            ->get();



        foreach ($simpananTransactions as $trx) {
            $result[] = [
                'tanggal' => $trx->tanggal,
                'jenis' => 'Simpanan',
                'jenis_transaksi' => ucfirst($trx->tipe) . ' Simpanan',
                'tipe_transaksi' => 'simpanan',
                'kategori' => ucfirst($trx->jenis),
                'jumlah' => $trx->tipe === 'setoran' ? $trx->jumlah : -$trx->jumlah,
                'status' => 'Berhasil',
                'id' => $trx->id,
            ];
        }

        // 2. Pinjaman (Transaksi Pinjaman)
        $pinjamanTransactions = Pinjaman::where('anggota_id', $user->id)
            ->whereDate('tanggal', '>=', $startDate)
            ->whereDate('tanggal', '<=', $endDate)
            ->orderBy('tanggal', 'desc')
            ->get();

        foreach ($pinjamanTransactions as $trx) {
            $jumlah = $trx->jumlah;
            $jenisTransaksi = $trx->tipe === 'pinjaman' ? 'Pencairan' : 'Angsuran';
            $isPencairan = $trx->tipe === 'pinjaman';
            
            // For pinjaman (disbursement), amount is positive, for angsuran (payment), amount is negative
            $jumlah = $isPencairan ? $jumlah : -$jumlah;
            
            $result[] = [
                'tanggal' => $trx->tanggal,
                'jenis' => 'Pinjaman',
                'jenis_transaksi' => $jenisTransaksi . ' Pinjaman',
                'tipe_transaksi' => 'pinjaman',
                'kategori' => ucfirst($trx->jenis),
                'jumlah' => $jumlah,
                'status' => 'Berhasil',
                'id' => $trx->id,
            ];
        }

        // Filter by type if specified
        if ($type !== 'all') {
            $result = array_filter($result, function($item) use ($type) {
                return strtolower($item['jenis']) === strtolower($type);
            });
        }

        // Sort results
        usort($result, function($a, $b) use ($sort, $order) {
            $valueA = $a[$sort] ?? '';
            $valueB = $b[$sort] ?? '';
            
            if ($valueA == $valueB) return 0;
            
            $compare = $valueA <=> $valueB;
            return $order === 'asc' ? $compare : -$compare;
        });

        // Manual pagination
        $perPage = 10;
        $currentPage = $request->input('page', 1);
        $currentItems = array_slice($result, ($currentPage - 1) * $perPage, $perPage);
        
        // Create paginator instance
        $transactions = new \Illuminate\Pagination\LengthAwarePaginator(
            $currentItems,
            count($result),
            $perPage,
            $currentPage,
            [
                'path' => \Illuminate\Support\Facades\Request::url(),
                'query' => $request->query(),
            ]
        );

        // Return as Inertia response
        return Inertia::render('Member/RiwayatTransaksi/Index', [
            'transactions' => $transactions,
            'filters' => $request->only(['start_date', 'end_date', 'bulan', 'tahun', 'type', 'sort', 'order']),
        ]);
    }
}
