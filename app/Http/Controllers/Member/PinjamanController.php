<?php

namespace App\Http\Controllers\Member;

use App\Http\Controllers\Controller;
use App\Models\PinjamanNew as Pinjaman;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class PinjamanController extends Controller
{
    public function index()
    {
        $userId = Auth::id();
        
        $year = (int)request()->input('tahun', now()->year);
        $month = (int)request()->input('bulan', now()->month);
        
        $startOfMonth = now()->year($year)->month($month)->startOfMonth()->toDateString();
        $endOfMonth = now()->year($year)->month($month)->endOfMonth()->toDateString();
        
        // Get transactions up to the end of selected month
        $transactionsUpToMonth = Pinjaman::where('anggota_id', $userId)
            ->where('tanggal', '<=', $endOfMonth)
            ->orderBy('tanggal')
            ->get();
            
        // Get transactions for the selected month only
        $monthlyTransactions = Pinjaman::where('anggota_id', $userId)
            ->whereBetween('tanggal', [$startOfMonth, $endOfMonth])
            ->orderBy('tanggal')
            ->get();
            
        $result = [];
        $validJenis = ['umum', 'khusus', 'emergency'];
        
        // Initialize result array
        foreach ($validJenis as $jenis) {
            $result[$jenis] = [
                'jenis' => 'Pinjaman ' . ucfirst($jenis),
                'pinjaman' => 0,               // Total pinjaman up to selected month
                'pinjaman_bulan_ini' => 0,     // Pinjaman in selected month
                'setoran' => 0,                // Total setoran up to selected month
                'setoran_bulan_ini' => 0,      // Setoran in selected month
                'sisa_pinjaman' => 0,          // Sisa pinjaman up to selected month
                'jsp' => 0                     // Jasa pinjaman in selected month
            ];
        }
        
        // Calculate cumulative values up to selected month
        foreach ($transactionsUpToMonth as $transaction) {
            $jenis = $transaction->jenis;
            $jumlah = (float) $transaction->jumlah;
            
            if (!in_array($jenis, $validJenis)) continue;
            
            if ($transaction->tipe === 'pinjaman') {
                $result[$jenis]['pinjaman'] += $jumlah;
                $result[$jenis]['sisa_pinjaman'] += $jumlah;
            } elseif ($transaction->tipe === 'setoran') {
                $result[$jenis]['setoran'] += $jumlah;
                $result[$jenis]['sisa_pinjaman'] = max(0, $result[$jenis]['sisa_pinjaman'] - $jumlah);
            }
        }
        
        // Calculate values for the selected month only
        foreach ($monthlyTransactions as $transaction) {
            $jenis = $transaction->jenis;
            $jumlah = (float) $transaction->jumlah;
            
            if (!in_array($jenis, $validJenis)) continue;
            
            if ($transaction->tipe === 'pinjaman') {
                $result[$jenis]['pinjaman_bulan_ini'] += $jumlah;
            } elseif ($transaction->tipe === 'setoran') {
                $result[$jenis]['setoran_bulan_ini'] += $jumlah;
            }
            
            // Sum JSP for this month and type
            if ($transaction->jsp && $transaction->jsp > 0) {
                $result[$jenis]['jsp'] += (float) $transaction->jsp;
            }
        }
        
        // For the frontend, we'll use the monthly values for the main display
        foreach ($validJenis as $jenis) {
            // Use monthly values for the main display
            $result[$jenis]['pinjaman'] = $result[$jenis]['pinjaman_bulan_ini'];
            $result[$jenis]['setoran'] = $result[$jenis]['setoran_bulan_ini'];
            // Sisa pinjaman is already calculated correctly for the selected month
        }
        
        $filteredResult = array_filter($result, function($item) {
            return $item['pinjaman'] > 0 || $item['setoran'] > 0;
        });
        
        $totalSisaPinjaman = array_sum(array_column($filteredResult, 'sisa_pinjaman'));
        $totalSetoranBulanIni = array_sum(array_column($filteredResult, 'setoran_bulan_ini'));
        $totalPinjamanBulanIni = array_sum(array_column($filteredResult, 'pinjaman_bulan_ini'));

        $startOfYear = now()->year($year)->startOfYear()->toDateString();
        
        $totalJspUmumSaatIni = Pinjaman::where('anggota_id', $userId)
            ->where('jenis', 'umum')
            ->whereBetween('tanggal', [$startOfYear, $endOfMonth])
            ->sum('jsp');
    
        $jspPerJenis = [
            'umum' => $result['umum']['jsp'],
            'khusus' => $result['khusus']['jsp'],
            'emergency' => $result['emergency']['jsp'],
        ];
    
        return Inertia::render('Member/Pinjaman/Index', [
            'pinjaman' => array_values($filteredResult),
            'summary' => [
                'total_sisa_pinjaman' => $totalSisaPinjaman,
                'total_setoran_bulan_ini' => $totalSetoranBulanIni,
                'total_pinjaman_bulan_ini' => $totalPinjamanBulanIni,
                'total_jsp_umum_saat_ini' => $totalJspUmumSaatIni
            ],
            'jsp_per_jenis' => $jspPerJenis,
            'filter' => [
                'bulan' => $month,
                'tahun' => $year,
                'nama_bulan' => now()->setMonth($month)->locale('id')->monthName,
                'tahun_list' => range(now()->year - 5, now()->year + 1),
            ]
        ]);
    }
}