<?php

namespace App\Http\Controllers\Member;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SimpananNew as Simpanan;
use Illuminate\Support\Facades\Auth;

class SimpananController extends Controller
{
    /**
     * Display a listing of the authenticated member's savings.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        $year = (int)$request->input('tahun', now()->year);
        $month = (int)$request->input('bulan', now()->month);
        
        $jenisSimpanan = Simpanan::select('jenis')
            ->where('anggota_id', $user->id)
            ->distinct()
            ->pluck('jenis');
        
        $data = [];
        $total_simpanan = 0;
        $total_setoran_bulan_ini = 0;
        $total_pengambilan_bulan_ini = 0;
        
        $startOfMonth = now()->year($year)->month($month)->startOfMonth()->toDateString();
        $endOfMonth = now()->year($year)->month($month)->endOfMonth()->toDateString();
        
        foreach ($jenisSimpanan as $jenis) {
            // Calculate cumulative balance from start of year to end of selected month
            $startOfYear = now()->year($year)->startOfYear()->toDateString();
            
            // Get all transactions from start of year to end of selected month
            $setoran = Simpanan::where('anggota_id', $user->id)
                ->where('jenis', $jenis)
                ->where('tipe', 'setoran')
                ->whereYear('tanggal', $year)
                ->whereMonth('tanggal', '<=', $month)
                ->sum('jumlah');
                
            $pengambilan = Simpanan::where('anggota_id', $user->id)
                ->where('jenis', $jenis)
                ->where('tipe', 'pengambilan')
                ->whereYear('tanggal', $year)
                ->whereMonth('tanggal', '<=', $month)
                ->sum('jumlah');
                
            $saldo = $setoran - $pengambilan;
            
            // Get transactions for the selected month only (for display purposes)
            $monthlySetoran = (float) Simpanan::where('anggota_id', $user->id)
                ->where('jenis', $jenis)
                ->where('tipe', 'setoran')
                ->whereBetween('tanggal', [$startOfMonth, $endOfMonth])
                ->sum('jumlah');
                
            $monthlyPengambilan = (float) Simpanan::where('anggota_id', $user->id)
                ->where('jenis', $jenis)
                ->where('tipe', 'pengambilan')
                ->whereBetween('tanggal', [$startOfMonth, $endOfMonth])
                ->sum('jumlah');
            
            $data[] = [
                'jenis_simpanan' => 'Simpanan ' . ucfirst($jenis),
                'setoran' => $monthlySetoran,
                'pengambilan' => $monthlyPengambilan,
                'saldo' => $saldo,
                'setoran_bulan_ini' => $monthlySetoran,
                'pengambilan_bulan_ini' => $monthlyPengambilan,
            ];
            
            $total_simpanan += $saldo;
            $total_setoran_bulan_ini += $monthlySetoran;
            $total_pengambilan_bulan_ini += $monthlyPengambilan;
        }

        return inertia('Member/Simpanan/Index', [
            'simpanan' => [
                'data' => $data,
                'total_simpanan' => $total_simpanan,
                'total_setoran_bulan_ini' => $total_setoran_bulan_ini,
                'total_pengambilan_bulan_ini' => $total_pengambilan_bulan_ini,
                'filter' => [
                    'bulan' => (int)$month,
                    'tahun' => (int)$year,
                    'nama_bulan' => now()->setMonth($month)->locale('id')->monthName,
                    'tahun_list' => range(now()->year - 5, now()->year + 1),
                ]
            ]
        ]);
    }

    /**
     * Show the details of a specific simpanan for the authenticated member.
     */
    public function show($jenis)
    {
        $user = Auth::user();
        
        $transactions = Simpanan::where('anggota_id', $user->id)
            ->where('jenis', $jenis)
            ->orderBy('tanggal', 'desc')
            ->get();
            
        $balance = 0;
        $transactions = $transactions->map(function($transaction) use (&$balance) {
            $balance += $transaction->tipe === 'setoran' ? $transaction->jumlah : -$transaction->jumlah;
            return [
                'tanggal' => $transaction->tanggal->format('d/m/Y'),
                'keterangan' => $transaction->keterangan,
                'debit' => $transaction->tipe === 'setoran' ? $transaction->jumlah : 0,
                'kredit' => $transaction->tipe === 'pengambilan' ? $transaction->jumlah : 0,
                'saldo' => $balance
            ];
        });
        
        return inertia('Member/Simpanan/Show', [
            'jenis' => 'Simpanan ' . ucfirst($jenis),
            'transactions' => $transactions
        ]);
    }
}