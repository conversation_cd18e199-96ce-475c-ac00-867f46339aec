<?php

namespace App\Http\Controllers;

use App\Models\Pinjaman;
use App\Models\TransaksiPinjaman;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PinjamanController extends Controller
{
    public function store(Request $request)
    {
        $validated = $request->validate([
            'nik' => 'required|string|exists:users,nik',
            'jenis_pinjaman' => 'required|in:umum,khusus,emergency',
            'tipe_transaksi' => 'required|in:pencairan,angsuran,denda,lainnya',
            'jumlah' => 'required|numeric|min:0',
            'bunga' => $request->tipe_transaksi === 'pencairan' ? 'required|numeric|min:0|max:100' : 'nullable|numeric|min:0|max:100',
            'keterangan' => 'nullable|string|max:255',
        ]);

        try {
            DB::beginTransaction();

            $user = User::where('nik', $validated['nik'])->firstOrFail();
            
            // Find or create loan record for disbursement
            if ($validated['tipe_transaksi'] === 'pencairan') {
                $pinjaman = Pinjaman::create([
                    'user_id' => $user->id,
                    'id_jenis_pinjaman' => $this->getJenisPinjamanId($validated['jenis_pinjaman']),
                    'jumlah_pinjaman' => $validated['jumlah'],
                    'bunga' => $validated['bunga'],
                    'tanggal_pinjaman' => now(),
                    'status' => 'Disetujui',
                ]);
                
                // Calculate and save JSP (Jasa Pinjaman)
                $jasaPinjaman = ($validated['jumlah'] * $validated['bunga'] / 100);
                TransaksiPinjaman::create([
                    'pinjaman_id' => $pinjaman->id,
                    'tipe_transaksi' => 'lainnya',
                    'jumlah' => $jasaPinjaman,
                    'tanggal_transaksi' => now(),
                    'keterangan' => 'Jasa Pinjaman (JSP) ' . $validated['bunga'] . '%',
                ]);
            } else {
                // For other transaction types, find the latest active loan
                $pinjaman = Pinjaman::where('user_id', $user->id)
                    ->where('status', 'Disetujui')
                    ->latest()
                    ->first();

                if (!$pinjaman) {
                    throw new \Exception('Tidak ada pinjaman aktif yang ditemukan untuk anggota ini.');
                }
            }

            // Create transaction record
            $transaksi = TransaksiPinjaman::create([
                'pinjaman_id' => $pinjaman->id,
                'tipe_transaksi' => $validated['tipe_transaksi'],
                'jumlah' => $validated['tipe_transaksi'] === 'angsuran' || $validated['tipe_transaksi'] === 'denda' 
                    ? -1 * abs($validated['jumlah']) // Make negative for payments
                    : abs($validated['jumlah']),     // Positive for disbursements
                'tanggal_transaksi' => now(),
                'keterangan' => $validated['keterangan'] ?? null,
            ]);

            // Update loan status based on payments
            $this->updateLoanStatus($pinjaman);

            DB::commit();

            if (request()->header('X-Inertia')) {
                return redirect()->back()->with('success', 'Transaksi pinjaman berhasil disimpan');
            }

            return response()->json([
                'message' => 'Transaksi berhasil disimpan',
                'data' => $transaksi
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            
            if (request()->header('X-Inertia')) {
                return back()->withErrors([
                    'error' => 'Terjadi kesalahan: ' . $e->getMessage()
                ]);
            }
            
            return response()->json([
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    private function getJenisPinjamanId($jenis)
    {
        return \App\Models\JenisPinjaman::firstOrCreate(
            ['nama_jenis' => $jenis],
            ['deskripsi' => 'Jenis pinjaman ' . $jenis]
        )->id_jenis_pinjaman;
    }

    private function updateLoanStatus($pinjaman)
    {
        $totalPinjaman = $pinjaman->transaksiPinjaman()
            ->where('tipe_transaksi', 'pencairan')
            ->sum('jumlah');
        
        $totalPembayaran = abs($pinjaman->transaksiPinjaman()
            ->whereIn('tipe_transaksi', ['angsuran', 'denda'])
            ->sum('jumlah'));
        
        if ($totalPembayaran >= $totalPinjaman) {
            $pinjaman->update(['status' => 'Lunas']);
        } elseif ($pinjaman->status !== 'Disetujui') {
            $pinjaman->update(['status' => 'Disetujui']);
        }
    }
    public function getActiveLoans($userId)
    {
        return Pinjaman::with(['transaksiPinjaman', 'jenisPinjaman'])
            ->where('user_id', $userId)
            ->where('status', 'Disetujui')
            ->get()
            ->map(function ($pinjaman) {
                // Total loan amount (pencairan is positive)
                $totalPinjaman = $pinjaman->transaksiPinjaman
                    ->where('tipe_transaksi', 'pencairan')
                    ->sum('jumlah');
                
                // Get all transactions
                $transactions = $pinjaman->transaksiPinjaman;
                
                // Calculate principal paid (only from angsuran)
                $totalAngsuran = abs($transactions
                    ->where('tipe_transaksi', 'angsuran')
                    ->sum('jumlah'));
                
                // Calculate JSP (Jasa Pinjaman) - look for 'lainnya' type with JSP in description
                $totalJSP = abs($transactions
                    ->filter(function($transaction) {
                        return $transaction->tipe_transaksi === 'lainnya' && 
                               str_contains($transaction->keterangan, 'Jasa Pinjaman (JSP)');
                    })
                    ->sum('jumlah'));
                
                // Calculate remaining principal (original loan - principal paid)
                $sisaPinjaman = max(0, $totalPinjaman - $totalAngsuran);
                
                // Total paid should only include principal payments, not JSP
                $totalDibayar = $totalAngsuran;
                
                return [
                    'id' => $pinjaman->id,
                    'jenis_pinjaman' => $pinjaman->jenisPinjaman->nama_jenis,
                    'total_pinjaman' => $totalPinjaman,
                    'total_angsuran' => $totalAngsuran,
                    'total_jsp' => $totalJSP,
                    'total_dibayar' => $totalDibayar,  // Only principal payments
                    'sisa_pinjaman' => $sisaPinjaman,
                    'bunga' => $pinjaman->bunga,
                ];
            });
    }

    public function index(Request $request)
    {
        // Get the selected month from the request or default to current month
        $selectedMonth = $request->input('month', now()->format('Y-m'));
        
        // Parse the selected month
        $date = Carbon::parse($selectedMonth . '-01');
        $startOfMonth = $date->copy()->startOfMonth()->toDateTimeString();
        $endOfMonth = $date->copy()->endOfMonth()->toDateTimeString();
        $previousMonth = $date->copy()->subMonth()->format('Y-m');
    
        // Get all users who have ever had loans
        $users = User::whereHas('pinjaman')
            ->with(['pinjaman' => function($query) use ($startOfMonth, $endOfMonth) {
                // Get loans created before or during the selected month
                $query->where('tanggal_pinjaman', '<=', $endOfMonth)
                      ->with(['transaksiPinjaman' => function($q) use ($startOfMonth, $endOfMonth) {
                          // Filter transactions for the selected month
                          $q->whereBetween('tanggal_transaksi', [$startOfMonth, $endOfMonth]);
                      }, 'jenisPinjaman']);
            }])->get();
    
        // Get previous month's data for all users
        $prevMonthStart = $date->copy()->subMonth()->startOfMonth()->toDateTimeString();
        $prevMonthEnd = $date->copy()->subMonth()->endOfMonth()->toDateTimeString();
        
        // Get all users who had transactions before the current month
        $usersWithHistory = User::whereHas('pinjaman', function($query) use ($prevMonthEnd) {
            $query->where('tanggal_pinjaman', '<=', $prevMonthEnd);
        })->with(['pinjaman' => function($query) use ($prevMonthEnd) {
            $query->where('tanggal_pinjaman', '<=', $prevMonthEnd)
                  ->with(['transaksiPinjaman', 'jenisPinjaman']);
        }])->get()->keyBy('id');

        // Process each user's data
        $reportData = [];
        
        foreach ($users as $user) {
            // Calculate previous month's ending balance
            $saldoAwal = 0;
            
            if ($usersWithHistory->has($user->id)) {
                $prevUser = $usersWithHistory->get($user->id);
                foreach ($prevUser->pinjaman as $prevPinjaman) {
                    // Get all transactions up to the end of the previous month
                    $pencairan = $prevPinjaman->transaksiPinjaman
                        ->where('tipe_transaksi', 'pencairan')
                        ->where('tanggal_transaksi', '<=', $prevMonthEnd)
                        ->sum('jumlah');
                    $angsuran = $prevPinjaman->transaksiPinjaman
                        ->where('tipe_transaksi', 'angsuran')
                        ->where('tanggal_transaksi', '<=', $prevMonthEnd)
                        ->sum('jumlah');
                    $bunga = $prevPinjaman->transaksiPinjaman
                        ->where('tipe_transaksi', 'lainnya')
                        ->where('tanggal_transaksi', '<=', $prevMonthEnd)
                        ->sum('jumlah');
                        
                    // Calculate running balance
                    $saldoAwal += ($pencairan - $angsuran + $bunga);
                }
            }

            // Initialize data for each user with calculated saldo_awal
            $userData = [
                'nik' => $user->nik ?? 'N/A',
                'name' => $user->name,
                'bagian' => $user->department ?? 'Umum',
                'saldo_awal' => $saldoAwal,
                'pinjaman_umum' => 0,
                'pinjaman_khusus' => 0,
                'pinjaman_emergency' => 0,
                'pemasukan_umum' => 0,
                'pemasukan_khusus' => 0,
                'pemasukan_emergency' => 0,
                'jsp_umum' => 0,
                'jsp_khusus' => 0,
                'jsp_emergency' => 0,
            ];
    
            // Process each loan
            foreach ($user->pinjaman as $pinjaman) {
                $jenis = strtolower($pinjaman->jenisPinjaman->nama_pinjaman ?? 'umum');
                
                // Calculate total loan amount (pencairan)
                $pencairan = $pinjaman->transaksiPinjaman
                    ->where('tipe_transaksi', 'pencairan')
                    ->sum('jumlah');
                    
                // Calculate total payments (angsuran)
                $angsuran = $pinjaman->transaksiPinjaman
                    ->where('tipe_transaksi', 'angsuran')
                    ->sum('jumlah');
                    
                // Calculate total interest (denda/JSP)
                $bunga = abs($pinjaman->transaksiPinjaman
                    ->filter(function($transaction) {
                        return $transaction->tipe_transaksi === 'denda' || 
                               ($transaction->tipe_transaksi === 'lainnya' && 
                                str_contains($transaction->keterangan ?? '', 'Jasa Pinjaman'));
                    })
                    ->sum('jumlah'));
    
                // Set values based on loan type
                $pinjamanKey = 'pinjaman_' . $jenis;
                $jspKey = 'jsp_' . $jenis;
                
                if (array_key_exists($pinjamanKey, $userData)) {
                    $userData[$pinjamanKey] += $pencairan;
                    $userData['pemasukan_' . str_replace('pinjaman_', '', $pinjamanKey)] += $angsuran;
                    $userData[$jspKey] = abs($bunga);
                } else {
                    // Default to 'umum' if loan type not recognized
                    $userData['pinjaman_umum'] += $pencairan;
                    $userData['pemasukan_umum'] += $angsuran;
                    $userData['jsp_umum'] = abs($bunga);
                }
            }
    
            $reportData[] = $userData;
        }
    
        // Sort report data by name for consistent ordering
        usort($reportData, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });

        return Inertia::render('Pinjaman/Index', [
            'pinjaman' => [
                'data' => $reportData,
                'selected_month' => $selectedMonth
            ]
        ]);
    }
}
