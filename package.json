{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@headlessui/react": "^2.2.4", "@inertiajs/react": "^2.0.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/vite": "^4.0.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.12", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.2.1", "vite": "^6.2.4"}, "dependencies": {"@heroicons/react": "^2.2.0", "date-fns": "^4.1.0", "lucide-react": "^0.522.0", "react-data-table-component": "^7.7.0", "react-helmet-async": "^2.0.5", "react-icons": "^5.5.0", "react-table": "^7.8.0", "react-toastify": "^11.0.5", "react-tooltip": "^5.29.1", "styled-components": "^6.1.19"}}