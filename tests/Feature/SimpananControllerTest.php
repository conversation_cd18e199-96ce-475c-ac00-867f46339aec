<?php

namespace Tests\Feature;

use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use App\Models\User;
use App\Models\JenisSimpanan;
use App\Models\Simpanan;
use App\Models\TransaksiSimpanan;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

class SimpananControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'nik' => '1234567890',
            'bagian' => 'IT',
            'status' => 'Aktif'
        ]);
        
        // Create jenis simpanan
        $this->pokok = JenisSimpanan::create(['nama_jenis' => 'Pokok', 'kode_jenis' => 'POKOK']);
        $this->wajib = JenisSimpanan::create(['nama_jenis' => 'Wajib', 'kode_jenis' => 'WAJIB']);
        $this->sukarela = JenisSimpanan::create(['nama_jenis' => 'Sukarela', 'kode_jenis' => 'SUKARELA']);
        $this->sihara = JenisSimpanan::create(['nama_jenis' => 'Sihara', 'kode_jenis' => 'SIHARA']);
        $this->deposito = JenisSimpanan::create(['nama_jenis' => 'Deposito', 'kode_jenis' => 'DEPOSITO']);
        
        // Create simpanan for the user - using the correct field names
        $this->simpananPokok = Simpanan::create([
            'user_id' => $this->user->id,
            'id_jenis_simpanan' => $this->pokok->id_jenis_simpanan,
            'tanggal_dibuat' => now(),
            'saldo' => 100000
        ]);
        
        $this->simpananWajib = Simpanan::create([
            'user_id' => $this->user->id,
            'id_jenis_simpanan' => $this->wajib->id_jenis_simpanan,
            'tanggal_dibuat' => now(),
            'saldo' => 200000
        ]);
        
        $this->simpananSukarela = Simpanan::create([
            'user_id' => $this->user->id,
            'id_jenis_simpanan' => $this->sukarela->id_jenis_simpanan,
            'tanggal_dibuat' => now(),
            'saldo' => 300000
        ]);
        
        // Create transactions for previous month
        $lastMonth = Carbon::now()->subMonth();
        
        // Previous month transactions (should be included in saldo_awal)
        TransaksiSimpanan::create([
            'simpanan_id' => $this->simpananPokok->id,
            'tanggal_transaksi' => $lastMonth->copy()->day(15),
            'tipe_transaksi' => 'Setoran',
            'jumlah' => 50000,
            'keterangan' => 'Setoran awal',
        ]);
        
        // Current month transactions
        TransaksiSimpanan::create([
            'simpanan_id' => $this->simpananWajib->id,
            'tanggal_transaksi' => now()->startOfMonth()->addDays(5),
            'tipe_transaksi' => 'Setoran',
            'jumlah' => 100000,
            'keterangan' => 'Setoran wajib',
        ]);
        
        TransaksiSimpanan::create([
            'simpanan_id' => $this->simpananSukarela->id,
            'tanggal_transaksi' => now()->startOfMonth()->addDays(10),
            'tipe_transaksi' => 'Setoran',
            'jumlah' => 150000,
            'keterangan' => 'Setoran sukarela',
        ]);
        
        TransaksiSimpanan::create([
            'simpanan_id' => $this->simpananSukarela->id,
            'tanggal_transaksi' => now()->startOfMonth()->addDays(15),
            'tipe_transaksi' => 'Pengambilan',
            'jumlah' => 50000,
            'keterangan' => 'Pengambilan sukarela',
        ]);
    }

    #[Test]
    public function it_can_view_simpanan_index_page()
    {
        $response = $this->actingAs($this->user)->get('/admin/simpanan');
        $response->assertOk();
    }

    #[Test]
    public function it_returns_correct_simpanan_data()
    {
        $currentMonth = now()->format('Y-m');
        
        $response = $this->actingAs($this->user)
            ->get("/admin/simpanan?month=$currentMonth");
            
        $response->assertOk();
        
        // Get the Inertia page data from the response
        $page = $response->original;
        $props = $page->getData()['page']['props'];
        $simpanan = $props['simpanan'];
        $data = $simpanan['data'][0] ?? null;
        
        // Assert saldo_awal (only previous month's transactions)
        $this->assertEquals(50000, $data['saldo_awal']);
        
        // Assert current month transactions
        $this->assertEquals('100000.00', $data['setoran_wajib']);
        $this->assertEquals('150000.00', $data['setoran_sukarela']);
        $this->assertEquals(0, $data['penarikan_sukarela']);
        
        // Assert calculated balances
        $this->assertEquals(50000, $data['pokok']);
        $this->assertEquals(100000, $data['wajib']);
        $this->assertEquals(150000, $data['sukarela']);
    }
    
    #[Test]
    public function it_filters_by_month_correctly()
    {
        $lastMonth = now()->subMonth()->format('Y-m');
        
        $response = $this->actingAs($this->user)
            ->get("/admin/simpanan?month=$lastMonth");
            
        $response->assertOk();
        
        // Get the Inertia page data from the response
        $page = $response->original;
        $props = $page->getData()['page']['props'];
        $simpanan = $props['simpanan'];
        $data = $simpanan['data'][0] ?? null;
        
        // Should only show transactions from last month
        $this->assertEquals(50000, $data['pokok']);
        $this->assertEquals(0, $data['wajib']);
        $this->assertEquals(0, $data['sukarela']);
    }
    
    #[Test]
    public function it_handles_no_transactions()
    {
        // Create a new user with no transactions
        $newUser = User::create([
            'name' => 'New Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'nik' => '**********',
            'bagian' => 'HRD',
            'status' => 'Aktif'
        ]);
        
        // Create a new jenis simpanan for this test
        $newJenisSimpanan = JenisSimpanan::create([
            'nama_jenis' => 'Test Simpanan',
            'deskripsi' => 'For testing only'
        ]);
        
        // Create a simpanan account for the new user
        $simpanan = Simpanan::create([
            'user_id' => $newUser->id,
            'id_jenis_simpanan' => $newJenisSimpanan->id_jenis_simpanan,
            'tanggal_dibuat' => now(),
            'saldo' => 0
        ]);
        
        $response = $this->actingAs($newUser)
            ->get('/admin/simpanan');
            
        $response->assertOk();
        
        // Get the Inertia page data from the response
        $page = $response->original;
        $props = $page->getData()['page']['props'];
        $simpanan = $props['simpanan'];
        
        // The user should have a simpanan account but with zero transactions
        $this->assertNotEmpty($simpanan['data']);
        $data = $simpanan['data'][0];
        $this->assertEquals(0, $data['pokok']);
        $this->assertEquals(0, $data['wajib']);
        $this->assertEquals(0, $data['sukarela']);
    }

    #[Test]
    public function it_handles_large_transaction_amounts()
    {
        // Create a large transaction
        TransaksiSimpanan::create([
            'simpanan_id' => $this->simpananSukarela->id,
            'tanggal_transaksi' => now(),
            'tipe_transaksi' => 'Setoran',
            'jumlah' => **********, // 1 billion
            'keterangan' => 'Large deposit',
        ]);

        $response = $this->actingAs($this->user)
            ->get('/admin/simpanan');
            
        $response->assertOk();
        
        $page = $response->original;
        $props = $page->getData()['page']['props'];
        $simpanan = $props['simpanan'];
        $data = $simpanan['data'][0];
        
        // Check if the large amount is handled correctly (formatted as string with 2 decimal places)
        $this->assertEquals('1000150000.00', $data['setoran_sukarela']);
    }
    
    #[Test]
    public function it_handles_invalid_month_parameter()
    {
        // Test with a valid month format to ensure the test setup is correct
        $validMonth = now()->format('Y-m');
        $response = $this->actingAs($this->user)
            ->get("/admin/simpanan?month=$validMonth");
            
        $response->assertOk();
        
        // Verify we can get the simpanan data with a valid month
        $page = $response->original;
        $props = $page->getData()['page']['props'];
        $this->assertArrayHasKey('simpanan', $props);
        
        // Test with invalid format - should throw InvalidFormatException
        $this->expectException(\Carbon\Exceptions\InvalidFormatException::class);
        
        $response = $this->actingAs($this->user)
            ->withoutExceptionHandling()
            ->get('/admin/simpanan?month=invalid-format');
    }
    
    #[Test]
    public function it_handles_invalid_month_number()
    {
        // Test with invalid month (month 13) - should default to current month
        $response = $this->actingAs($this->user)
            ->withoutExceptionHandling()
            ->get('/admin/simpanan?month=2023-13');
            
        $response->assertOk();
        
        // Should still return the simpanan data
        $page = $response->original;
        $props = $page->getData()['page']['props'];
        $this->assertArrayHasKey('simpanan', $props);
    }
    public function it_handles_future_month_parameter()
    {
        $futureMonth = now()->addYear()->format('Y-m');
        
        $response = $this->actingAs($this->user)
            ->get("/admin/simpanan?month=$futureMonth");
            
        $response->assertOk();
        
        $page = $response->original;
        $props = $page->getData()['page']['props'];
        $simpanan = $props['simpanan'];
        
        // For future months, we should still get the user's data but with zero or existing balances
        $this->assertNotEmpty($simpanan['data']);
        $data = $simpanan['data'][0];
        
        // Verify the data structure is correct
        $this->assertArrayHasKey('pokok', $data);
        $this->assertArrayHasKey('wajib', $data);
        $this->assertArrayHasKey('sukarela', $data);
    }

    #[Test]
    public function it_handles_multiple_transactions_same_day()
    {
        // First, get the current balance
        $initialResponse = $this->actingAs($this->user)
            ->get('/admin/simpanan');
        $initialData = $initialResponse->original->getData()['page']['props']['simpanan']['data'][0];
        $initialSukarela = $initialData['sukarela'];
        
        // Create multiple transactions on the same day
        $today = now();
        
        TransaksiSimpanan::create([
            'simpanan_id' => $this->simpananSukarela->id,
            'tanggal_transaksi' => $today,
            'tipe_transaksi' => 'Setoran',
            'jumlah' => 50000,
            'keterangan' => 'First transaction',
        ]);
        
        TransaksiSimpanan::create([
            'simpanan_id' => $this->simpananSukarela->id,
            'tanggal_transaksi' => $today,
            'tipe_transaksi' => 'Setoran',
            'jumlah' => 75000,
            'keterangan' => 'Second transaction',
        ]);
        
        $response = $this->actingAs($this->user)
            ->get('/admin/simpanan');
            
        $response->assertOk();
        
        $page = $response->original;
        $props = $page->getData()['page']['props'];
        $simpanan = $props['simpanan'];
        $data = $simpanan['data'][0];
        
        // Should include the initial balance plus the new transactions
        $expectedTotal = $initialSukarela + 50000 + 75000;
        $this->assertEquals($expectedTotal, $data['sukarela']);
    }
}
