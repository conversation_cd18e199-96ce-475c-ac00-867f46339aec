import React from 'react';

const CurrencyFormat = ({ value, className = '' }) => {
    const numValue = Number(value) || 0;
    const isNegative = numValue < 0;
    const absoluteValue = Math.abs(numValue);
    
    const formattedValue = new Intl.NumberFormat('id-ID', { 
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(absoluteValue);

    let displayValue = `Rp ${formattedValue}`;
    
    if (isNegative) {
        displayValue = `(Rp ${formattedValue})`;
    }

    return (
        <span className={`${className} ${isNegative ? 'text-red-600' : ''}`}>
            {displayValue}
        </span>
    );
};

export default CurrencyFormat;
