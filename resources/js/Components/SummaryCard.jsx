import React from 'react';

const SummaryCard = ({ 
    title, 
    value, 
    icon, 
    color = 'blue',
    className = ''
}) => {
    const colorClasses = {
        green: 'bg-green-500',
        blue: 'bg-blue-500',
        yellow: 'bg-yellow-500',
        red: 'bg-red-500',
        indigo: 'bg-indigo-500',
        purple: 'bg-purple-500',
        pink: 'bg-pink-500',
    };
    
    return (
        <div className={`bg-white overflow-hidden shadow rounded-lg h-full ${className}`}>
            <div className="p-4 sm:p-5">
                <div className="flex items-center">
                    <div className={`flex-shrink-0 ${colorClasses[color] || 'bg-blue-500'} rounded-md p-2 sm:p-3`}>
                        {icon}
                    </div>
                    <div className="ml-3 sm:ml-4 w-0 flex-1">
                        <dt className="text-xs sm:text-sm font-medium text-gray-500 truncate">
                            {title}
                        </dt>
                        <dd className="text-lg sm:text-xl md:text-2xl font-semibold text-gray-900 truncate">
                            {value}
                        </dd>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SummaryCard;
