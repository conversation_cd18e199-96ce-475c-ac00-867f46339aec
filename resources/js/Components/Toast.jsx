import { useEffect } from 'react';
import { X } from 'lucide-react';

export default function Toast({ message, type = 'success', onClose }) {
    useEffect(() => {
        const timer = setTimeout(() => {
            onClose();
        }, 5000);

        return () => clearTimeout(timer);
    }, [onClose]);

    const typeStyles = {
        success: 'bg-green-100 border-green-400 text-green-700',
        error: 'bg-red-100 border-red-400 text-red-700',
        warning: 'bg-yellow-100 border-yellow-400 text-yellow-700',
        info: 'bg-blue-100 border-blue-400 text-blue-700',
    };

    return (
        <div 
            className={`fixed top-4 right-4 z-50 border-l-4 p-4 rounded shadow-lg ${typeStyles[type]} flex items-center justify-between min-w-64`}
            role="alert"
        >
            <div className="flex-1">
                <p className="text-sm">{message}</p>
            </div>
            <button
                type="button"
                onClick={onClose}
                className="ml-4 text-gray-500 hover:text-gray-700"
                aria-label="Close"
            >
                <X className="h-4 w-4" />
            </button>
        </div>
    );
}

export function useToast() {
    const showToast = (message, type = 'success') => {
        const toastContainer = document.getElementById('toast-container');
        if (!toastContainer) return;

        const toast = document.createElement('div');
        toast.className = 'toast-item';
        
        const toastElement = document.createElement('div');
        toast.appendChild(toastElement);
        
        const root = createRoot(toastElement);
        
        const removeToast = () => {
            toast.classList.add('fade-out');
            setTimeout(() => {
                root.unmount();
                toast.remove();
            }, 300);
        };
        
        root.render(
            <Toast 
                message={message} 
                type={type} 
                onClose={removeToast} 
            />
        );
        
        toastContainer.appendChild(toast);
        
        return removeToast;
    };

    return { showToast };
}
