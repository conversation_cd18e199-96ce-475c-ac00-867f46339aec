import React from 'react';

const months = [
    { value: 1, name: '<PERSON><PERSON><PERSON>' },
    { value: 2, name: '<PERSON><PERSON><PERSON>' },
    { value: 3, name: '<PERSON><PERSON>' },
    { value: 4, name: 'April' },
    { value: 5, name: '<PERSON>' },
    { value: 6, name: '<PERSON><PERSON>' },
    { value: 7, name: '<PERSON><PERSON>' },
    { value: 8, name: '<PERSON><PERSON><PERSON>' },
    { value: 9, name: 'September' },
    { value: 10, name: '<PERSON><PERSON><PERSON>' },
    { value: 11, name: 'November' },
    { value: 12, name: '<PERSON><PERSON><PERSON>' },
];

export default function MonthFilterMember({ 
    selectedMonth, 
    selectedYear,
    onMonthChange,
    onYearChange,
    onApply,
    onReset,
    title = 'Filter Data',
    description = 'Pilih periode untuk melihat data',
    showTypeFilter = false,
    showSearch = false,
    typeValue = 'all',
    onTypeChange = () => {},
    searchValue = '',
    onSearchChange = () => {},
    onSearchKeyDown = () => {}
}) {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let year = currentYear - 5; year <= currentYear + 1; year++) {
        years.push(year);
    }

    return (
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
            <div className="mb-3">
                <h3 className="text-sm sm:text-base font-medium text-gray-900">{title}</h3>
                <p className="text-xs text-gray-500">{description}</p>
            </div>
            
            <div className="space-y-3">
                {/* Search Input - Conditional */}
                {showSearch && (
                    <div>
                        <input
                            type="text"
                            value={searchValue}
                            onChange={onSearchChange}
                            onKeyDown={onSearchKeyDown}
                            placeholder="Cari transaksi..."
                            className="focus:ring-indigo-500 focus:border-indigo-500 block w-full px-3 py-2 text-sm border-gray-300 rounded-md"
                        />
                    </div>
                )}

                {/* Month and Year Filter */}
                <div className="grid grid-cols-2 gap-3">
                    <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">Bulan</label>
                        <select
                            value={selectedMonth}
                            onChange={(e) => onMonthChange(parseInt(e.target.value))}
                            className="focus:ring-indigo-500 focus:border-indigo-500 block w-full px-3 py-2 text-sm border-gray-300 rounded-md"
                        >
                            {months.map((month) => (
                                <option key={month.value} value={month.value}>
                                    {month.name}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">Tahun</label>
                        <select
                            value={selectedYear}
                            onChange={(e) => onYearChange(parseInt(e.target.value))}
                            className="focus:ring-indigo-500 focus:border-indigo-500 block w-full px-3 py-2 text-sm border-gray-300 rounded-md"
                        >
                            {years.map((year) => (
                                <option key={year} value={year}>
                                    {year}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>

                {/* Type Filter (Conditional) */}
                {showTypeFilter && (
                    <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">Tipe Transaksi</label>
                        <select
                            value={typeValue}
                            onChange={onTypeChange}
                            className="focus:ring-indigo-500 focus:border-indigo-500 block w-full px-3 py-2 text-sm border-gray-300 rounded-md"
                        >
                            <option value="all">Semua Tipe</option>
                            <option value="simpanan">Simpanan</option>
                            <option value="pinjaman">Pinjaman</option>
                        </select>
                    </div>
                )}

                {/* Action Buttons */}
                <div className="flex items-center space-x-2 pt-1">
                    <button 
                        type="button"
                        onClick={onReset}
                        className="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 h-[38px]"
                    >
                        Reset
                    </button>
                    <button 
                        type="button"
                        onClick={onApply}
                        className="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 h-[38px]"
                    >
                        Terapkan
                    </button>
                </div>
            </div>
        </div>
    );
}
