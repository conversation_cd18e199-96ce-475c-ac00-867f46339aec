import { createContext, useContext, useState } from 'react';
import Toast from '../Components/Toast';

export const ToastContext = createContext({
    showToast: () => {},
});

export const ToastProvider = ({ children }) => {
    const [toast, setToast] = useState(null);

    const showToast = (message, type = 'success') => {
        setToast({ message, type });
        
        // Auto hide after 5 seconds
        setTimeout(() => {
            setToast(null);
        }, 5000);
    };

    return (
        <ToastContext.Provider value={{ showToast }}>
            {children}
            {toast && (
                <Toast
                    message={toast.message}
                    type={toast.type}
                    onClose={() => setToast(null)}
                />
            )}
        </ToastContext.Provider>
    );
};

export const useToast = () => {
    return useContext(ToastContext);
};
