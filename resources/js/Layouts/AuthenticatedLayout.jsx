import ApplicationLogo from '@/Components/ApplicationLogo';
import Dropdown from '@/Components/Dropdown';
import { Link, usePage } from '@inertiajs/react';
import { useState, useEffect } from 'react';
import { 
    FaHome, 
    FaUsers, 
    FaMoneyBillWave, 
    FaChartLine, 
    FaCog, 
    FaBars, 
    FaTimes,
    FaUserCog,
    FaClipboardList,
    FaHistory,
    FaBell
} from 'react-icons/fa';

// Komponen Sidebar Item
const SidebarItem = ({ icon: Icon, label, active, children, ...props }) => (
    <Link
        {...props}
        className={`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
            active 
                ? 'bg-blue-50 text-blue-600' 
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
        }`}
    >
        <Icon className="text-lg mr-3" />
        <span>{children || label}</span>
    </Link>
);

export default function AuthenticatedLayout({ header, children }) {
    const user = usePage().props.auth.user;
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const [isMobile, setIsMobile] = useState(false);

    // Cek ukuran layar saat komponen dimuat dan saat window di-resize
    useEffect(() => {
        const handleResize = () => {
            const mobile = window.innerWidth < 1024;
            setIsMobile(mobile);
            setSidebarOpen(!mobile);
        };

        // Set initial value
        handleResize();

        // Add event listener
        window.addEventListener('resize', handleResize);

        // Clean up
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // Menu navigasi admin
    const adminNavigation = [
        { 
            name: 'Dashboard', 
            href: route('dashboard'), 
            icon: FaHome, 
            active: route().current('dashboard') 
        },
        { 
            name: 'Anggota', 
            href: route('admin.anggota.index'), 
            icon: FaUsers, 
            active: route().current('admin.anggota.index') 
        },
        // { 
        //     name: 'Simpanan', 
        //     href: route('admin.simpanan.index'), 
        //     icon: FaMoneyBillWave, 
        //     active: route().current('admin.simpanan.index') 
        // },
        // { 
        //     name: 'Pinjaman', 
        //     href: route('admin.pinjaman.index'), 
        //     icon: FaClipboardList, 
        //     active: route().current('admin.pinjaman.index') 
        // },
        // { 
        //     name: 'Laporan', 
        //     href: route('admin.laporan.index'), 
        //     icon: FaChartLine, 
        //     active: route().current('admin.laporan.index') 
        // },
        { 
            name: 'Riwayat', 
            href: route('admin.riwayat.index'), 
            icon: FaHistory, 
            active: route().current('admin.riwayat.index') 
        },
        { 
            name: 'Pengaturan', 
            href: route('pengaturan.index'), 
            icon: FaCog, 
            active: route().current('pengaturan.index') 
        },
    ];

    // Menu navigasi member
    const memberNavigation = [
        { 
            name: 'Simpanan Saya', 
            href: route('member.simpanan.index'), 
            icon: FaMoneyBillWave, 
            active: route().current('member.simpanan.index')
        },
        { 
            name: 'Pinjaman Saya', 
            href: route('member.pinjaman.index'), 
            icon: FaClipboardList, 
            active: route().current('member.pinjaman.index')
        },
        { 
            name: 'Riwayat Transaksi', 
            href: route('member.riwayat.index'), 
            icon: FaHistory, 
            active: route().current('member.riwayat.index')
        },
    ];

    // Tentukan navigasi berdasarkan role
    const navigation = user?.role === 'admin' ? adminNavigation : memberNavigation;

    return (
        <div className="min-h-screen bg-gray-50 flex">
            {/* Mobile sidebar backdrop */}
            {isMobile && sidebarOpen && (
                <div 
                    className="fixed inset-0 bg-black bg-opacity-50 z-20 lg:hidden"
                    onClick={() => setSidebarOpen(false)}
                />
            )}

            {/* Sidebar */}
            <div 
                className={`fixed lg:sticky top-0 left-0 h-screen w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out z-30 ${
                    sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
                }`}
            >
                <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                    <Link href="/" className="flex items-center">
                        <ApplicationLogo className="h-8 w-auto text-blue-600" />
                        <span className="ml-2 text-xl font-semibold text-gray-800">Koperasi Hegar Mulya</span>
                    </Link>
                    <button 
                        onClick={() => setSidebarOpen(false)}
                        className="lg:hidden text-gray-500 hover:text-gray-700"
                    >
                        <FaTimes className="h-5 w-5" />
                    </button>
                </div>

                <nav className="p-4 space-y-1">
                    {navigation.map((item) => {
                        // Skip rendering if route is not defined (for member view on admin routes)
                        if (!item.href) return null;
                        
                        return (
                            <SidebarItem
                                key={item.name}
                                href={item.href}
                                icon={item.icon}
                                active={item.active}
                                onClick={() => isMobile && setSidebarOpen(false)}
                            >
                                {item.name}
                            </SidebarItem>
                        );
                    })}
                </nav>
            </div>

            {/* Main Content */}
            <div className="flex-1 flex flex-col overflow-hidden">
                {/* Top Navigation */}
                <header className="bg-white shadow-sm z-10">
                    <div className="flex items-center justify-between px-4 py-3 sm:px-6 lg:px-8">
                        <div className="flex items-center">
                            <button 
                                onClick={() => setSidebarOpen(!sidebarOpen)}
                                className="lg:hidden text-gray-500 hover:text-gray-700 mr-2"
                            >
                                <FaBars className="h-6 w-6" />
                            </button>
                            {header && (
                                <h1 className="text-xl font-semibold text-gray-900">
                                    {header}
                                </h1>
                            )}
                        </div>

                        {/* User Dropdown */}
                        <div className="flex items-center">
                            
                            <div className="ml-3 relative">
                                <Dropdown>
                                    <Dropdown.Trigger>
                                        <button className="flex items-center space-x-2 focus:outline-none">
                                            <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                                                <span className="text-blue-600 font-medium">
                                                    {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                                                </span>
                                            </div>
                                            <span className="hidden md:inline-block text-sm font-medium text-gray-700">
                                                {user?.name || 'User'}
                                            </span>
                                            <svg className="h-4 w-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                            </svg>
                                        </button>
                                    </Dropdown.Trigger>

                                    <Dropdown.Content>
                                        <Dropdown.Link href={route('profile.edit')}>
                                            <FaUserCog className="inline mr-2" />
                                            Profil Saya
                                        </Dropdown.Link>
                                        <Dropdown.Link href={route('logout')} method="post" as="button">
                                            Keluar
                                        </Dropdown.Link>
                                    </Dropdown.Content>
                                </Dropdown>
                            </div>
                        </div>
                    </div>
                </header>

                {/* Main Content */}
                <main className="flex-1 overflow-y-auto p-4 sm:p-6 lg:p-8">
                    {children}
                </main>
            </div>
        </div>
    );
}
