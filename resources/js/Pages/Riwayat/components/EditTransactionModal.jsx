import { useForm, router } from '@inertiajs/react';
import { useEffect, useState } from 'react';
import InputLabel from '@/Components/InputLabel';
import TextInput from '@/Components/TextInput';
import InputError from '@/Components/InputError';
import TextArea from '@/Components/TextArea';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';

const formatCurrency = (value) => {
    if (!value) return 'Rp 0';
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(value);
};
export default function EditTransactionModal({ 
    show, 
    onClose, 
    transaction, 
    onSuccess, 
    onError 
}) {
    const [isLoading, setIsLoading] = useState(false);
    
    // Initialize form with transaction data or empty values
    const { data, setData, put, processing, errors, reset } = useForm({
        id: transaction?.id || '',
        tanggal: transaction?.tanggal ? format(new Date(transaction.tanggal), 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd'),
        jenis_transaksi: transaction?.jenis_transaksi || '',
        tipe_transaksi: transaction?.tipe_transaksi || '',
        jumlah: transaction?.jumlah || '',
        bunga: transaction?.bunga || '',
        total_bunga: transaction?.total_bunga || 0,
        total_pinjaman: transaction?.total_pinjaman || 0,
        keterangan: transaction?.keterangan || ''
    });

    // Calculate interest and total for loans when amount or interest rate changes
    useEffect(() => {
        if (data.jenis_transaksi === 'pinjaman' && data.tipe_transaksi === 'pencairan') {
            const amount = parseFloat(data.jumlah) || 0;
            const interestRate = parseFloat(data.bunga) || 0;
            const interest = (amount * interestRate) / 100;
            const total = amount + interest;
            
            setData({
                ...data,
                total_bunga: interest.toFixed(2),
                total_pinjaman: total.toFixed(2)
            });
        }
    }, [data.jumlah, data.bunga, data.jenis_transaksi, data.tipe_transaksi]);

    // Update form when transaction prop changes
    useEffect(() => {
        if (transaction) {
            reset({
                id: transaction.id,
                tanggal: transaction.tanggal ? format(new Date(transaction.tanggal), 'yyyy-MM-dd') : '',
                jenis_transaksi: transaction.jenis_transaksi,
                tipe_transaksi: transaction.tipe_transaksi,
                jumlah: transaction.jumlah,
                bunga: transaction.bunga || '',
                total_bunga: transaction.total_bunga || 0,
                total_pinjaman: transaction.total_pinjaman || 0,
                keterangan: transaction.keterangan || ''
            });
        }
    }, [transaction]);

    const handleSubmit = (e) => {
        e.preventDefault();
        setIsLoading(true);

        const routeName = data.jenis_transaksi === 'simpanan' 
            ? 'simpanan.update' 
            : 'pinjaman.update';

        router.put(route(routeName, data.id), data, {
            onSuccess: () => {
                reset();
                onClose();
                if (onSuccess) onSuccess('Transaksi berhasil diperbarui');
            },
            onError: (errors) => {
                console.error('Error updating transaction:', errors);
                const errorMessage = errors?.message || 'Gagal memperbarui transaksi';
                if (onError) onError(new Error(errorMessage));
            },
            onFinish: () => {
                setIsLoading(false);
            },
            preserveScroll: true
        });
    };

    if (!show) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center p-4 z-50 overflow-y-auto">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl my-8">
                <form onSubmit={handleSubmit} className="p-6 max-h-[calc(100vh-4rem)] overflow-y-auto">
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-lg font-medium text-gray-900">
                            Edit Transaksi {data.jenis_transaksi === 'simpanan' ? 'Simpanan' : 'Pinjaman'}
                        </h2>
                        <button
                            type="button"
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-500"
                        >
                            <span className="sr-only">Tutup</span>
                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <div className="grid grid-cols-1 gap-6">
                        {/* Transaction Type */}
                        <div>
                            <InputLabel htmlFor="jenis_transaksi" value="Jenis Transaksi *" />
                            <select
                                id="jenis_transaksi"
                                value={data.jenis_transaksi}
                                onChange={(e) => setData('jenis_transaksi', e.target.value)}
                                className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                disabled={!!transaction?.id}
                            >
                                <option value="">Pilih Jenis Transaksi</option>
                                <option value="simpanan">Simpanan</option>
                                <option value="pinjaman">Pinjaman</option>
                            </select>
                            <InputError message={errors.jenis_transaksi} className="mt-2" />
                        </div>

                        {/* Transaction Date */}
                        <div>
                            <InputLabel htmlFor="tanggal" value="Tanggal Transaksi *" />
                            <TextInput
                                id="tanggal"
                                type="date"
                                value={data.tanggal}
                                onChange={(e) => setData('tanggal', e.target.value)}
                                className="mt-1 block w-full"
                                required
                            />
                            <InputError message={errors.tanggal} className="mt-2" />
                        </div>

                        {/* Transaction Type (Setoran/Penarikan or Pencairan/Pelunasan) */}
                        <div>
                            <InputLabel htmlFor="tipe_transaksi" value="Tipe Transaksi *" />
                            <select
                                id="tipe_transaksi"
                                value={data.tipe_transaksi}
                                onChange={(e) => setData('tipe_transaksi', e.target.value)}
                                className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                required
                            >
                                <option value="">Pilih Tipe Transaksi</option>
                                {data.jenis_transaksi === 'simpanan' ? (
                                    <>
                                        <option value="Setoran">Setoran</option>
                                        <option value="Penarikan">Penarikan</option>
                                    </>
                                ) : (
                                    <>
                                        <option value="pencairan">Pencairan</option>
                                        <option value="pelunasan">Pelunasan</option>
                                    </>
                                )}
                            </select>
                            <InputError message={errors.tipe_transaksi} className="mt-2" />
                        </div>

                        {/* Amount */}
                        <div>
                            <InputLabel 
                                htmlFor="jumlah" 
                                value={`Jumlah ${data.jenis_transaksi === 'simpanan' ? 'Simpanan' : 'Pinjaman'} *`} 
                            />
                            <div className="mt-1 relative rounded-md shadow-sm">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 sm:text-sm">Rp</span>
                                </div>
                                <TextInput
                                    id="jumlah"
                                    type="number"
                                    value={data.jumlah}
                                    onChange={(e) => setData('jumlah', e.target.value)}
                                    className="pl-10 block w-full"
                                    placeholder="0"
                                    required
                                />
                            </div>
                            <p className="mt-1 text-sm text-gray-500">
                                {formatCurrency(data.jumlah)}
                            </p>
                            <InputError message={errors.jumlah} className="mt-2" />
                        </div>

                        {/* Interest Rate (for loans) */}
                        {data.jenis_transaksi === 'pinjaman' && data.tipe_transaksi === 'pencairan' && (
                            <div>
                                <InputLabel htmlFor="bunga" value="Bunga (%) *" />
                                <div className="mt-1 relative rounded-md shadow-sm">
                                    <TextInput
                                        id="bunga"
                                        type="number"
                                        value={data.bunga}
                                        onChange={(e) => setData('bunga', e.target.value)}
                                        className="block w-full pr-12"
                                        placeholder="0"
                                        step="0.01"
                                        min="0"
                                        required
                                    />
                                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <span className="text-gray-500 sm:text-sm">%</span>
                                    </div>
                                </div>
                                <InputError message={errors.bunga} className="mt-2" />
                                
                                {/* Total Interest and Loan Amount (for loans) */}
                                <div className="mt-4 space-y-2">
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Total Bunga:</span>
                                        <span className="text-sm font-medium">
                                            {formatCurrency(data.total_bunga)}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Total Pinjaman:</span>
                                        <span className="text-sm font-medium">
                                            {formatCurrency(data.total_pinjaman)}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Description */}
                        <div>
                            <InputLabel htmlFor="keterangan" value="Keterangan" />
                            <TextArea
                                id="keterangan"
                                value={data.keterangan}
                                onChange={(e) => setData('keterangan', e.target.value)}
                                className="mt-1 block w-full"
                                rows={3}
                                placeholder="Tambahkan catatan (opsional)"
                            />
                            <InputError message={errors.keterangan} className="mt-2" />
                        </div>
                    </div>

                    <div className="mt-8 flex justify-end space-x-3">
                        <button
                            type="button"
                            onClick={onClose}
                            className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            disabled={processing}
                        >
                            Batal
                        </button>
                        <button
                            type="submit"
                            className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                            disabled={processing || isLoading}
                        >
                            {isLoading ? (
                                <>
                                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Menyimpan...
                                </>
                            ) : 'Simpan Perubahan'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}