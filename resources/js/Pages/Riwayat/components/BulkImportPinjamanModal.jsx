import { router } from '@inertiajs/react';
import { useState, useRef } from 'react';
import { toast } from 'react-toastify';
import axios from 'axios';

export default function BulkImportPinjamanModal({ show, onClose, onSuccess }) {
    const [file, setFile] = useState(null);
    const [isUploading, setIsUploading] = useState(false);
    const fileInputRef = useRef(null);

    const handleFileChange = (e) => {
        setFile(e.target.files[0]);
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        
        if (!file) {
            toast.error('Silakan pilih file Excel terlebih dahulu');
            return;
        }

        const formData = new FormData();
        formData.append('file', file);
        
        setIsUploading(true);

        axios.post(route('admin.import.process', { type: 'pinjaman' }), formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
        .then(response => {
            toast.success(`Berhasil mengimport ${response.data.imported} data pinjaman`);
            onSuccess();
            handleClose();
        })
        .catch(error => {
            if (error.response?.status === 422) {
                // Handle validation errors
                const errorMessages = error.response.data.errors || [];
                errorMessages.forEach(message => {
                    toast.error(message, { autoClose: 10000 });
                });
            } else {
                // Handle other errors
                const errorMessage = error.response?.data?.message || 'Terjadi kesalahan saat mengimport data';
                toast.error(errorMessage);
            }
        })
        .finally(() => {
            setIsUploading(false);
        });
    };

    const handleClose = () => {
        setFile(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
        onClose();
    };

    const downloadTemplate = () => {
        window.location.href = route('admin.import.template', { type: 'pinjaman' });
    };

    if (!show) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg w-full max-w-md p-6">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium">Import Data pinjaman</h3>
                    <button onClick={handleClose} className="text-gray-500 hover:text-gray-700">
                        &times;
                    </button>
                </div>

                <div className="mb-6">
                    <p className="text-sm text-gray-600 mb-4">
                        Unduh template Excel terlebih dahulu, isi data sesuai format, lalu unggah file Excel yang sudah diisi.
                    </p>
                    
                    <div className="flex flex-col space-y-4">
                        <button
                            type="button"
                            onClick={downloadTemplate}
                            className="inline-flex items-center justify-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150"
                        >
                            Unduh Template Excel
                        </button>

                        <div className="mt-4">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Pilih File Excel
                            </label>
                            <input
                                type="file"
                                ref={fileInputRef}
                                onChange={handleFileChange}
                                accept=".xlsx, .xls, .csv"
                                className="block w-full text-sm text-gray-500
                                    file:mr-4 file:py-2 file:px-4
                                    file:rounded-md file:border-0
                                    file:text-sm file:font-semibold
                                    file:bg-blue-50 file:text-blue-700
                                    hover:file:bg-blue-100"
                            />
                        </div>
                    </div>
                </div>

                <div className="flex justify-end space-x-3">
                    <button
                        type="button"
                        onClick={handleClose}
                        disabled={isUploading}
                        className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                        Batal
                    </button>
                    <button
                        type="button"
                        onClick={handleSubmit}
                        disabled={!file || isUploading}
                        className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                        {isUploading ? 'Mengunggah...' : 'Import Data'}
                    </button>
                </div>
            </div>
        </div>
    );
}
