import React, { useState, useEffect } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { format, startOfMonth, endOfMonth } from 'date-fns';
import { id } from 'date-fns/locale';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import CurrencyFormat from '@/Components/CurrencyFormat';
import EditTransactionModal from './components/EditTransactionModal';
import BulkImportSimpananModal from './components/BulkImportSimpananModal';
import BulkImportPinjamanModal from './components/BulkImportPinjamanModal';
import { Dialog } from '@headlessui/react';
import { Pencil, Trash2, Loader2 } from 'lucide-react';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

export default function Riwayat({ auth, transactions: transactionsProp, filters }) {
    const [showEditModal, setShowEditModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [selectedTransaction, setSelectedTransaction] = useState(null);
    const [isDeleting, setIsDeleting] = useState(false);
    const [showBulkImportModal, setShowBulkImportModal] = useState(false);
    const [showBulkImportPinjamanModal, setShowBulkImportPinjamanModal] = useState(false);
    const [selectedItems, setSelectedItems] = useState([]);
    const [isBulkDeleting, setIsBulkDeleting] = useState(false);
    const [showBulkDeleteModal, setShowBulkDeleteModal] = useState(false);
    
    const handleSuccess = (message = 'Transaksi berhasil disimpan') => {
        toast.success(message);
        router.reload({ only: ['transactions'] });
    };

    const handleError = (error) => {
        const errorMessage = error?.message || 'Terjadi kesalahan saat memproses transaksi';
        toast.error(errorMessage);
    };

    const handleSelectAll = (e) => {
        if (e.target.checked) {
            const allIds = transactions.data.map(t => `${t.jenis.toLowerCase()}-${t.id}`);
            setSelectedItems(allIds);
        } else {
            setSelectedItems([]);
        }
    };

    const handleSelectItem = (id) => {
        setSelectedItems(prevSelected => {
            if (prevSelected.includes(id)) {
                return prevSelected.filter(item => item !== id);
            } else {
                return [...prevSelected, id];
            }
        });
    };

    const confirmBulkDelete = () => {
        if (selectedItems.length === 0) return;

        setIsBulkDeleting(true);
        router.post(route('admin.riwayat.bulkDestroy'), {
            ids: selectedItems
        }, {
            preserveScroll: true,
            onSuccess: () => {
                toast.success(`${selectedItems.length} transaksi berhasil dihapus`);
                setSelectedItems([]);
                setShowBulkDeleteModal(false);
                router.reload({ only: ['transactions'] });
            },
            onError: (errors) => {
                const errorMessage = Object.values(errors).join(' ');
                toast.error(errorMessage || 'Gagal menghapus transaksi terpilih');
            },
            onFinish: () => {
                setIsBulkDeleting(false);
            }
        });
    };

    const formatDate = (dateString) => {
        if (!dateString) return '-';
        const options = { day: '2-digit', month: 'short', year: 'numeric' };
        return new Date(dateString).toLocaleDateString('id-ID', options);
    };
    const transactions = transactionsProp?.data ? transactionsProp : { data: [] };
    
    const getDefaultDates = () => {
        const today = new Date();
        return {
            start_date: format(startOfMonth(today), 'yyyy-MM-dd'),
            end_date: format(endOfMonth(today), 'yyyy-MM-dd')
        };
    };

    const [sortConfig, setSortConfig] = useState({
        key: 'tanggal',
        direction: 'desc',
    });

    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [params, setParams] = useState({
        start_date: filters.start_date || getDefaultDates().start_date,
        end_date: filters.end_date || getDefaultDates().end_date,
        search: filters.search || '',
        type: filters.type || 'all',
        sort: filters.sort || 'tanggal',
        order: filters.order || 'desc',
    });
    
    // Debounce search
    useEffect(() => {
        const timer = setTimeout(() => {
            handleFilter({ search: searchQuery, page: 1 });
        }, 500);
        return () => clearTimeout(timer);
    }, [searchQuery]);

    useEffect(() => {   
        const initialParams = {};
        
        if (filters.sort && filters.order) {
            setSortConfig({
                key: filters.sort,
                direction: filters.order,
            });
        } else if (!filters.start_date || !filters.end_date) {
            initialParams.start_date = getDefaultDates().start_date;
            initialParams.end_date = getDefaultDates().end_date;
        }
        
        if (Object.keys(initialParams).length > 0) {
            handleFilter(initialParams);
        }
    }, []);


    const handleSort = (key) => {
        const direction = sortConfig.key === key && sortConfig.direction === 'asc' ? 'desc' : 'asc';
        const newSortConfig = { key, direction };
        setSortConfig(newSortConfig);
        
        handleFilter({
            sort: key,
            order: direction,
            page: 1,
        });
    };

    const handleFilter = (newParams = {}) => {
        let { start_date, end_date } = { ...params, ...newParams };
        
        if (start_date && end_date && new Date(start_date) > new Date(end_date)) {
            [start_date, end_date] = [end_date, start_date];
        }
        
        const updatedParams = { 
            ...params,
            ...newParams,
            start_date,
            end_date,
            sort: newParams.sort !== undefined ? newParams.sort : sortConfig.key,
            order: newParams.order !== undefined ? newParams.order : sortConfig.direction,
        };
        
        setParams(updatedParams);
        
        const cleanParams = Object.fromEntries(
            Object.entries(updatedParams).filter(([_, v]) => v !== undefined && v !== '')
        );
        
        router.get(route('admin.riwayat.index'), cleanParams, {
            preserveState: true,
            preserveScroll: true,
            only: ['transactions', 'filters'],
        });
    };

    return (
        <AuthenticatedLayout
            user={auth.user}
            header={
                <div className="font-semibold text-xl text-gray-800 leading-tight">
                    Riwayat Transaksi
                </div>
            }
        >
            <Head title="Riwayat Transaksi" />
            <div className="py-4">
                <div className="max-w-full mx-auto px-2 sm:px-4 lg:px-6">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-4 sm:p-5">
                            {/* Filter Section */}
                            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
                                <div className="flex justify-between items-start mb-3">
                                    <div>
                                        <h3 className="text-lg font-medium text-gray-900">Filter Riwayat Transaksi</h3>
                                        <p className="text-sm text-gray-500">Filter berdasarkan periode dan tipe transaksi</p>
                                    </div>
                                    <div className="flex space-x-2">
                                        <button
                                            type="button"
                                            onClick={() => setShowBulkImportModal(true)}
                                            className="inline-flex items-center px-4 py-2 bg-purple-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-purple-700 focus:bg-purple-700 active:bg-purple-800 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition ease-in-out duration-150 ml-2"
                                        >
                                            Import Simpanan Excel
                                        </button>
                                        <button
                                            type="button"
                                            onClick={() => setShowBulkImportPinjamanModal(true)}
                                            className="inline-flex items-center px-4 py-2 bg-purple-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-purple-700 focus:bg-purple-700 active:bg-purple-800 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition ease-in-out duration-150 ml-2"
                                        >
                                            Import Pinjaman Excel
                                        </button>
                                    </div>
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-12 gap-3">
                                    <div className="md:col-span-3">
                                        <div className="relative">
                                            <input
                                                type="text"
                                                value={searchQuery}
                                                onChange={(e) => setSearchQuery(e.target.value)}
                                                placeholder="Cari transaksi..."
                                                className="focus:ring-indigo-500 focus:border-indigo-500 block w-full px-3 py-2 text-sm border-gray-300 rounded-md"
                                            />
                                            {searchQuery && (
                                                <button
                                                    type="button"
                                                    onClick={() => {
                                                        setSearchQuery('');
                                                        handleFilter({ search: '' });
                                                    }}
                                                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                                                >
                                                    <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                    </svg>
                                                </button>
                                            )}
                                        </div>
                                    </div>
                                    <div className="md:col-span-5 flex items-center space-x-2">
                                        <input
                                            type="date"
                                            value={params.start_date}
                                            onChange={(e) => setParams({ ...params, start_date: e.target.value })}
                                            className="focus:ring-indigo-500 focus:border-indigo-500 block w-full px-3 py-2 text-sm border-gray-300 rounded-md"
                                        />
                                        <span className="text-gray-500 whitespace-nowrap">s/d</span>
                                        <input
                                            type="date"
                                            value={params.end_date}
                                            onChange={(e) => setParams({ ...params, end_date: e.target.value })}
                                            className="focus:ring-indigo-500 focus:border-indigo-500 block w-full px-3 py-2 text-sm border-gray-300 rounded-md"
                                        />
                                    </div>
                                    <div className="md:col-span-3">
                                        <select
                                            value={params.type}
                                            onChange={(e) => handleFilter({ type: e.target.value })}
                                            className="focus:ring-indigo-500 focus:border-indigo-500 block w-full px-3 py-2 text-sm border-gray-300 rounded-md"
                                        >
                                            <option value="all">Semua Tipe</option>
                                            <option value="simpanan">Simpanan</option>
                                            <option value="pinjaman">Pinjaman</option>
                                        </select>
                                    </div>
                                    <div className="md:col-span-1">
                                        <button 
                                            onClick={() => handleFilter()}
                                            className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                        >
                                            Cari
                                        </button>
                                    </div>
                                </div>
                            </div>

                            {/* Bulk delete button */}
                            {selectedItems.length > 0 && (
                                <div className="mb-4">
                                    <button
                                        type="button"
                                        onClick={() => setShowBulkDeleteModal(true)}
                                        className="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150"
                                    >
                                        Hapus ({selectedItems.length}) Transaksi Terpilih
                                    </button>
                                </div>
                            )}

                            {/* Transactions Table */}
                            {transactions.data.length > 0 ? (
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th className="p-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    <input
                                                        type="checkbox"
                                                        className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out"
                                                        onChange={handleSelectAll}
                                                        checked={transactions.data.length > 0 && selectedItems.length === transactions.data.length}
                                                        ref={el => el && (el.indeterminate = selectedItems.length > 0 && selectedItems.length < transactions.data.length)}
                                                    />
                                                </th>
                                                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                                                    No
                                                </th>
                                                <th 
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                                    onClick={() => handleSort('tanggal')}
                                                >
                                                    <div className="flex items-center">
                                                        Tanggal
                                                        {sortConfig.key === 'tanggal' && (
                                                            <span className="ml-1">
                                                                {sortConfig.direction === 'asc' ? '↑' : '↓'}
                                                            </span>
                                                        )}
                                                    </div>
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Nama Anggota
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Jenis
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Tipe
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Kategori
                                                </th>
                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Jumlah
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Status
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Keterangan
                                                </th>
                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Aksi
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {transactions.data.map((transaction, index) => (
                                                <tr key={`${transaction.jenis.toLowerCase()}-${transaction.id}`} className="hover:bg-gray-50">
                                                    <td className="p-4 whitespace-nowrap">
                                                        <input
                                                            type="checkbox"
                                                            className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out"
                                                            value={`${transaction.jenis.toLowerCase()}-${transaction.id}`}
                                                            checked={selectedItems.includes(`${transaction.jenis.toLowerCase()}-${transaction.id}`)}
                                                            onChange={() => handleSelectItem(`${transaction.jenis.toLowerCase()}-${transaction.id}`)}
                                                        />
                                                    </td>
                                                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                                        {(transactions.current_page - 1) * transactions.per_page + index + 1}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {formatDate(transaction.tanggal)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {transaction.anggota?.nama || transaction.nama_anggota || '-'}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {transaction.jenis}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {transaction.tipe}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {transaction.nama_jenis}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right">
                                                        <div className="space-y-1">
                                                            <CurrencyFormat 
                                                                value={transaction.tipe === 'Angsuran' || transaction.tipe === 'Pengambilan'
                                                                    ? -Math.abs(transaction.jumlah) 
                                                                    : transaction.jumlah} 
                                                                className="block text-sm text-gray-900" 
                                                            />
                                                            {transaction.jumlah_bunga > 0 && (
                                                                <div className="text-xs text-gray-500">
                                                                    Bunga: <CurrencyFormat value={transaction.jumlah_bunga} className="inline" />
                                                                </div>
                                                            )}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                            transaction.status === 'Berhasil' || transaction.status === 'Lunas'
                                                                ? 'bg-green-100 text-green-800'
                                                                : transaction.status === 'Menunggu' || transaction.status === 'Disetujui'
                                                                ? 'bg-yellow-100 text-yellow-800'
                                                                : 'bg-red-100 text-red-800'
                                                        }`}>
                                                            {transaction.status || 'Berhasil'}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                        {transaction.keterangan || '-'}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                        <div className="flex justify-end space-x-2">
                                                            {/* <button
                                                                onClick={() => {
                                                                    setSelectedTransaction(transaction);
                                                                    setShowEditModal(true);
                                                                }}
                                                                className="text-indigo-600 hover:text-indigo-900"
                                                                title="Edit"
                                                            >
                                                                <Pencil className="h-4 w-4" />
                                                            </button> */}
                                                            <button
                                                                onClick={() => {
                                                                    setSelectedTransaction(transaction);
                                                                    setShowDeleteModal(true);
                                                                }}
                                                                className="text-red-600 hover:text-red-900"
                                                                title="Hapus"
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                    
                                    {/* Pagination */}
                                    {transactions.last_page > 1 && (
                                        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                                            <div className="flex-1 flex justify-between sm:hidden">
                                                <a
                                                    href={transactions.prev_page_url ? `${window.location.pathname}?${new URLSearchParams({
                                                        ...Object.fromEntries(new URLSearchParams(window.location.search)),
                                                        page: transactions.current_page - 1
                                                    })}` : '#'}
                                                    className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                                                        !transactions.prev_page_url ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'
                                                    }`}
                                                >
                                                    Sebelumnya
                                                </a>
                                                <a
                                                    href={transactions.next_page_url ? `${window.location.pathname}?${new URLSearchParams({
                                                        ...Object.fromEntries(new URLSearchParams(window.location.search)),
                                                        page: transactions.current_page + 1
                                                    })}` : '#'}
                                                    className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                                                        !transactions.next_page_url ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'
                                                    }`}
                                                >
                                                    Selanjutnya
                                                </a>
                                            </div>
                                            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                                <div>
                                                    <p className="text-sm text-gray-700">
                                                        Menampilkan <span className="font-medium">{transactions.from || 0}</span> ke <span className="font-medium">{transactions.to || 0}</span> dari{' '}
                                                        <span className="font-medium">{transactions.total || 0}</span> hasil
                                                    </p>
                                                </div>
                                                <div>
                                                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                                        <button
                                                            onClick={() => handleFilter({ page: transactions.current_page - 1 })}
                                                            disabled={!transactions.prev_page_url}
                                                            className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-r-0 border-gray-300 bg-white text-sm font-medium ${
                                                                !transactions.prev_page_url ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                                                            }`}
                                                        >
                                                            <span className="sr-only">Previous</span>
                                                            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                                <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                                                            </svg>
                                                        </button>
                                                        {(() => {
                                                            const pages = [];
                                                            const totalPages = transactions.last_page;
                                                            const currentPage = transactions.current_page;
                                                            const maxVisiblePages = 5; 
                                                            
                                                            pages.push(1);
                                                            
                                                            if (currentPage > maxVisiblePages - 1) {
                                                                pages.push('...');
                                                            }
                                                            
                                                      
                                                            let startPage = Math.max(2, currentPage - 1);
                                                            let endPage = Math.min(totalPages - 1, currentPage + 1);
                                                            
                                                            if (currentPage <= maxVisiblePages - 1) {
                                                                endPage = maxVisiblePages;
                                                            }
                                                            
                                                      
                                                            if (currentPage >= totalPages - (maxVisiblePages - 2)) {
                                                                startPage = totalPages - (maxVisiblePages - 1);
                                                            }
                                         
                                                            for (let i = startPage; i <= endPage; i++) {
                                                                if (i > 1 && i < totalPages) {
                                                                    pages.push(i);
                                                                }
                                                            }
                                                            
                                                            if (currentPage < totalPages - (maxVisiblePages - 2)) {
                                                                if (currentPage < totalPages - (maxVisiblePages - 1)) {
                                                                    pages.push('...');
                                                                }
                                                                pages.push(totalPages);
                                                            } else if (totalPages > 1) {
                                                                pages.push(totalPages);
                                                            }
                                                            
                                                            return pages.map((page, index) => {
                                                                if (page === '...') {
                                                                    return (
                                                                        <span key={`ellipsis-${index}`} className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                                                            ...
                                                                        </span>
                                                                    );
                                                                }
                                                                
                                                                return (
                                                                    <button
                                                                        key={page}
                                                                        onClick={() => handleFilter({ page })}
                                                                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                                                            transactions.current_page === page
                                                                                ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                                                                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                                                        }`}
                                                                    >
                                                                        {page}
                                                                    </button>
                                                                );
                                                            });
                                                        })()}
                                                        <button
                                                            onClick={() => handleFilter({ page: transactions.current_page + 1 })}
                                                            disabled={!transactions.next_page_url}
                                                            className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                                                                !transactions.next_page_url ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                                                            }`}
                                                        >
                                                            <span className="sr-only">Next</span>
                                                            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                                            </svg>
                                                        </button>
                                                    </nav>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <svg
                                        className="mx-auto h-12 w-12 text-gray-400"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                        aria-hidden="true"
                                    >
                                        <path
                                            vectorEffect="non-scaling-stroke"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z"
                                        />
                                    </svg>
                                    <h3 className="mt-2 text-sm font-medium text-gray-900">Tidak ada transaksi</h3>
                                    <p className="mt-1 text-sm text-gray-500">
                                        {params.search || params.start_date || params.end_date || params.type !== 'all'
                                            ? 'Tidak ada transaksi yang sesuai dengan filter yang dipilih.'
                                            : 'Belum ada transaksi yang tercatat.'}
                                    </p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Edit Transaction Modal */}
            <EditTransactionModal
                show={showEditModal}
                transaction={selectedTransaction}
                onClose={() => {
                    setShowEditModal(false);
                    setSelectedTransaction(null);
                }}
                onSuccess={() => {
                    setShowEditModal(false);
                    router.reload({ only: ['transactions'] });
                }}
            />

            {/* Bulk Import Simpanan Modal */}
            <BulkImportSimpananModal
                show={showBulkImportModal}
                onClose={() => setShowBulkImportModal(false)}
                onSuccess={() => {
                    setShowBulkImportModal(false);
                    router.reload({ only: ['transactions'] });
                }}
            />

            {/* Bulk Import Pinjaman Modal */}
            <BulkImportPinjamanModal
                show={showBulkImportPinjamanModal}
                onClose={() => setShowBulkImportPinjamanModal(false)}
                onSuccess={() => {
                    setShowBulkImportPinjamanModal(false);
                    router.reload({ only: ['transactions'] });
                }}
            />

            {/* Delete Confirmation Dialog */}
            <Dialog
                open={showDeleteModal}
                onClose={() => setShowDeleteModal(false)}
                className="relative z-50"
            >
                <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
                <div className="fixed inset-0 flex items-center justify-center p-4">
                    <Dialog.Panel className="mx-auto max-w-sm rounded bg-white p-6">
                        <Dialog.Title className="text-lg font-medium text-gray-900 mb-4">
                            Hapus Transaksi
                        </Dialog.Title>
                        <Dialog.Description className="text-gray-600 mb-6">
                            Apakah Anda yakin ingin menghapus transaksi ini? Tindakan ini tidak dapat dibatalkan.
                        </Dialog.Description>
                        <div className="flex justify-end space-x-3">
                            <button
                                onClick={() => setShowDeleteModal(false)}
                                disabled={isDeleting}
                                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                            >
                                Batal
                            </button>
                            <button
                                onClick={() => {
                                    if (!selectedTransaction) return;
                                    setIsDeleting(true);
                                    
                                    router.delete(route('admin.riwayat.destroy', [selectedTransaction.jenis.toLowerCase(), selectedTransaction.id]), {
                                        preserveScroll: true,
                                        onSuccess: () => {
                                            toast.success('Transaksi berhasil dihapus');
                                            router.reload({ only: ['transactions'] });
                                        },
                                        onError: (errors) => {
                                            const errorMessage = errors?.message || 'Gagal menghapus transaksi';
                                            toast.error(errorMessage);
                                        },
                                        onFinish: () => {
                                            setIsDeleting(false);
                                            setShowDeleteModal(false);
                                            setSelectedTransaction(null);
                                        }
                                    });
                                }}
                                disabled={isDeleting}
                                className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 flex items-center"
                            >
                                {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                {isDeleting ? 'Menghapus...' : 'Hapus'}
                            </button>
                        </div>
                    </Dialog.Panel>
                </div>
            </Dialog>

            {/* Bulk Delete Confirmation Dialog */}
            <Dialog
                open={showBulkDeleteModal}
                onClose={() => setShowBulkDeleteModal(false)}
                className="relative z-50"
            >
                <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
                <div className="fixed inset-0 flex items-center justify-center p-4">
                    <Dialog.Panel className="mx-auto max-w-sm rounded bg-white p-6">
                        <Dialog.Title className="text-lg font-medium text-gray-900 mb-4">
                            Hapus Transaksi Terpilih
                        </Dialog.Title>
                        <Dialog.Description className="text-gray-600 mb-6">
                            Apakah Anda yakin ingin menghapus {selectedItems.length} transaksi terpilih? Tindakan ini tidak dapat dibatalkan.
                        </Dialog.Description>
                        <div className="flex justify-end space-x-3">
                            <button
                                onClick={() => setShowBulkDeleteModal(false)}
                                disabled={isBulkDeleting}
                                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                            >
                                Batal
                            </button>
                            <button
                                onClick={confirmBulkDelete}
                                disabled={isBulkDeleting}
                                className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 flex items-center"
                            >
                                {isBulkDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                {isBulkDeleting ? 'Menghapus...' : 'Hapus'}
                            </button>
                        </div>
                    </Dialog.Panel>
                </div>
            </Dialog>
        </AuthenticatedLayout>
    );
}
