import React, { useState, useEffect } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import CurrencyFormat from '@/Components/CurrencyFormat';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import MonthFilter from '@/Components/MonthFilter';
import { format } from 'date-fns';

export default function Simpanan({ simpanan = { data: [] }, auth = {} }) {
    const [selectedMonth, setSelectedMonth] = useState(format(new Date(), 'yyyy-MM'));
    const [filteredData, setFilteredData] = useState(simpanan?.data || []);

    useEffect(() => {
        // Filter data berdasarkan bulan yang dipilih
        router.get(route('admin.simpanan.index'), { month: selectedMonth }, {
            preserveState: true,
            only: ['simpanan'],
            onSuccess: (page) => {
                const simpananData = page.props.simpanan?.data || [];
                setFilteredData(simpananData);
            },
            onError: () => {
                setFilteredData([]);
            }
        });
    }, [selectedMonth]);



    return (
        <AuthenticatedLayout
            user={auth?.user}
            header={
                <div className="font-semibold text-xl text-gray-800 leading-tight">
                    Data Simpanan <span className="text-red-600">(DALAM PENGEMBANGAN)</span>
                </div>
            }
        >
            <div className="py-4">
                <div className="max-w-full mx-auto px-2 sm:px-4 lg:px-6">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-4 sm:p-5">
                            <MonthFilter 
                                selectedMonth={selectedMonth}
                                onMonthChange={(e) => setSelectedMonth(e.target.value)}
                                title="Filter Data Simpanan"
                                description="Pilih periode untuk melihat data simpanan"
                            />
                            <div className="overflow-x-auto w-full" style={{ WebkitOverflowScrolling: 'touch' }}>
                                <table className="min-w-max w-full bg-white border border-gray-200 text-sm" style={{ tableLayout: 'auto' }}>
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th rowSpan="2" className="border px-4 py-2 text-center whitespace-nowrap">NO</th>
                                            <th rowSpan="2" className="border px-4 py-2 text-left whitespace-nowrap">NIK</th>
                                            <th rowSpan="2" className="border px-4 py-2 text-left whitespace-nowrap">Nama</th>
                                            <th rowSpan="2" className="border px-4 py-2 text-left whitespace-nowrap">Bagian</th>
                                            <th rowSpan="2" className="border px-4 py-2 text-right whitespace-nowrap">Saldo Awal</th>
                                            
                                            <th colSpan="5" className="border text-center bg-blue-50">PEMASUKAN</th>
                                            <th colSpan="5" className="border text-center bg-red-50">PENGELUARAN</th>
                                            <th rowSpan="2" className="border px-4 py-2 text-right bg-yellow-50 whitespace-nowrap">Total Penjamin Pinjaman</th>
                                            <th rowSpan="2" className="border px-4 py-2 text-right bg-green-50 whitespace-nowrap">Total</th>
                                        </tr>
                                        <tr>
                                            {/* Subkolom Pemasukan */}
                                            <th className="border px-4 py-2 text-right bg-blue-50">Pokok</th>
                                            <th className="border px-4 py-2 text-right bg-blue-50">Wajib</th>
                                            <th className="border px-4 py-2 text-right bg-blue-50">Sukarela</th>
                                            <th className="border px-4 py-2 text-right bg-blue-50">Sihara</th>
                                            <th className="border px-4 py-2 text-right bg-blue-50">Deposito</th>
                                            
                                            {/* Subkolom Pengeluaran */}
                                            <th className="border px-4 py-2 text-right bg-red-50">Pokok</th>
                                            <th className="border px-4 py-2 text-right bg-red-50">Wajib</th>
                                            <th className="border px-4 py-2 text-right bg-red-50">Sukarela</th>
                                            <th className="border px-4 py-2 text-right bg-red-50">Sihara</th>
                                            <th className="border px-4 py-2 text-right bg-red-50">Deposito</th>

                                        </tr>
                                    </thead>
                                    <tbody>
                                        {filteredData.map((item, index) => {
                                            // Gunakan total_semua dari backend
                                            const total = item.total_semua || 0;
                                            
                                            return (
                                                <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                                                    <td className="border px-2 py-2 text-center whitespace-nowrap">{index + 1}</td>
                                                    <td className="border px-4 py-2 whitespace-nowrap">{item.nik}</td>
                                                    <td className="border px-4 py-2 whitespace-nowrap">{item.name}</td>
                                                    <td className="border px-4 py-2 whitespace-nowrap">{item.bagian}</td>
                                                    <td className="border px-4 py-2 text-right">
                                                        <CurrencyFormat value={item.saldo_awal} className="block text-right w-full" />
                                                    </td>
                                                    
                                                    {/* Kolom Pemasukan */}
                                                    <td className="border px-4 py-2 text-right">
                                                        <CurrencyFormat value={item.setoran_pokok} className="block text-right w-full" />
                                                    </td>
                                                    <td className="border px-4 py-2 text-right">
                                                        <CurrencyFormat value={item.setoran_wajib} className="block text-right w-full" />
                                                    </td>
                                                    <td className="border px-4 py-2 text-right">
                                                        <CurrencyFormat value={item.setoran_sukarela} className="block text-right w-full" />
                                                    </td>
                                                    <td className="border px-4 py-2 text-right">
                                                        <CurrencyFormat value={item.setoran_sihara} className="block text-right w-full" />
                                                    </td>
                                                    <td className="border px-4 py-2 text-right">
                                                        <CurrencyFormat value={item.setoran_deposito} className="block text-right w-full" />
                                                    </td>
                                                    
                                                    {/* Kolom Pengeluaran */}
                                                    <td className="border px-4 py-2 text-right">
                                                        <CurrencyFormat value={item.penarikan_pokok} className="block text-right w-full" />
                                                    </td>
                                                    <td className="border px-4 py-2 text-right">
                                                        <CurrencyFormat value={item.penarikan_wajib} className="block text-right w-full" />
                                                    </td>
                                                    <td className="border px-4 py-2 text-right">
                                                        <CurrencyFormat value={item.penarikan_sukarela} className="block text-right w-full" />
                                                    </td>
                                                    <td className="border px-4 py-2 text-right">
                                                        <CurrencyFormat value={item.penarikan_sihara} className="block text-right w-full" />
                                                    </td>
                                                    <td className="border px-4 py-2 text-right">
                                                        <CurrencyFormat value={item.penarikan_deposito} className="block text-right w-full" />
                                                    </td>
                                                    
                                                    {/* Total Penjamin Pinjaman */}
                                                    <td className="border px-4 py-2 text-right font-semibold bg-yellow-50">
                                                        <CurrencyFormat value={item.total_penjamin} className="block text-right w-full" />
                                                    </td>
                                                    
                                                    {/* Total */}
                                                    <td className="border px-4 py-2 text-right font-semibold bg-green-50">
                                                        <CurrencyFormat value={total} className="block text-right w-full" />
                                                    </td>
                                                </tr>
                                            );
                                        })}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
