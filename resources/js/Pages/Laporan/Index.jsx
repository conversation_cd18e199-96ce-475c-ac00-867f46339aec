import React, { useState, useEffect } from 'react';
import axios from 'axios';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head, router } from '@inertiajs/react';
import { format, startOfMonth, endOfMonth, parseISO } from 'date-fns';
import CurrencyFormat from '@/Components/CurrencyFormat';

export default function Laporan({ auth, laporan = { member: null, transactions: [] } }) {
    const [dateRange, setDateRange] = useState({
        start: format(startOfMonth(new Date()), 'yyyy-MM-dd'),
        end: format(endOfMonth(new Date()), 'yyyy-MM-dd')
    });

    const [search, setSearch] = useState('');
    const [searchQuery, setSearchQuery] = useState('');
    const [searchResults, setSearchResults] = useState([]);
    const [showDropdown, setShowDropdown] = useState(false);
    const searchTimeout = React.useRef(null);

    // Search for members when search query changes
    useEffect(() => {
        if (searchQuery.length > 1) {
            clearTimeout(searchTimeout.current);
            searchTimeout.current = setTimeout(() => {
                axios.get(route('admin.laporan.search-members'), {
                    params: { search: searchQuery }
                }).then(response => {
                    setSearchResults(response.data);
                    setShowDropdown(response.data.length > 0);
                });
            }, 300);
        } else {
            setSearchResults([]);
            setShowDropdown(false);
        }

        return () => clearTimeout(searchTimeout.current);
    }, [searchQuery]);

    const handleSearch = (e) => {
        e.preventDefault();
        const query = searchQuery.trim();
        if (!query) return;
        
        // If the search is in "Name (NIK)" format, extract just the name
        const searchTerm = query.includes('(') ? query.split('(')[0].trim() : query;
        
        router.get(route('admin.laporan.index'), {
            start_date: dateRange.start,
            end_date: dateRange.end,
            search: searchTerm
        }, {
            preserveState: true,
            preserveScroll: true,
            onSuccess: () => {
                // Update the search input to show the full name if a member was selected
                if (searchResults.length === 1) {
                    const member = searchResults[0];
                    setSearch(`${member.name} (${member.nik})`);
                }
            }
        });
    };

    const selectMember = (member) => {
        const memberName = `${member.name} (${member.nik})`;
        setSearch(memberName);
        setSearchQuery(memberName);
        setShowDropdown(false);
        
        // Trigger search after selecting a member
        router.get(route('admin.laporan.index'), {
            start_date: dateRange.start,
            end_date: dateRange.end,
            search: memberName
        }, {
            preserveState: true,
            preserveScroll: true
        });
    };



    return (
        <AuthenticatedLayout
            user={auth.user}
            header={
                <div className="font-semibold text-xl text-gray-800 leading-tight">
                    Laporan Transaksi Anggota <span className="text-red-600">(DALAM PENGEMBANGAN)</span>
                </div>
            }
        >
            <Head title="Laporan Keuangan" />
            <div className="py-4">
                <div className="max-w-full mx-auto px-2 sm:px-4 lg:px-6">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-4 sm:p-5">
                            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
                                <div className="mb-3">
                                    <h3 className="text-lg font-medium text-gray-900">Filter Laporan Keuangan</h3>
                                    <p className="text-sm text-gray-500">Filter berdasarkan periode dan data anggota</p>
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-12 gap-3">
                                    <div className="md:col-span-4 relative">
                                        <input
                                            type="text"
                                            value={search}
                                            onChange={(e) => {
                                                const value = e.target.value;
                                                setSearch(value);
                                                setSearchQuery(value);
                                                if (value.length > 1) {
                                                    setShowDropdown(true);
                                                } else {
                                                    setShowDropdown(false);
                                                }
                                            }}
                                            onFocus={() => searchQuery.length > 1 && setShowDropdown(true)}
                                            onBlur={() => setTimeout(() => setShowDropdown(false), 200)}
                                            onKeyDown={(e) => {
                                                if (e.key === 'Enter') {
                                                    e.preventDefault();
                                                    handleSearch(e);
                                                }
                                            }}
                                            placeholder="Cari NIK/Nama..."
                                            className="focus:ring-indigo-500 focus:border-indigo-500 block w-full px-3 py-2 text-sm border-gray-300 rounded-md"
                                        />
                                        {showDropdown && searchResults.length > 0 && (
                                            <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
                                                {searchResults.map((member) => (
                                                    <div
                                                        key={member.id}
                                                        className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                                                        onMouseDown={() => selectMember(member)}
                                                    >
                                                        <div className="font-medium">{member.name}</div>
                                                        <div className="text-xs text-gray-500">NIK: {member.nik} • {member.bagian || '-'}</div>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                    <div className="md:col-span-6 flex items-center space-x-2">
                                        <input
                                            type="date"
                                            value={dateRange.start}
                                            onChange={(e) => setDateRange({...dateRange, start: e.target.value})}
                                            className="focus:ring-indigo-500 focus:border-indigo-500 block w-full px-3 py-2 text-sm border-gray-300 rounded-md"
                                        />
                                        <span className="text-gray-500 whitespace-nowrap">s/d</span>
                                        <input
                                            type="date"
                                            value={dateRange.end}
                                            onChange={(e) => setDateRange({...dateRange, end: e.target.value})}
                                            className="focus:ring-indigo-500 focus:border-indigo-500 block w-full px-3 py-2 text-sm border-gray-300 rounded-md"
                                        />
                                    </div>
                                    <div className="md:col-span-2">
                                        <button 
                                            type="button"
                                            onClick={handleSearch}
                                            disabled={!searchQuery.trim()}
                                            className={`w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${searchQuery.trim() ? 'bg-indigo-600 hover:bg-indigo-700' : 'bg-indigo-300 cursor-not-allowed'}`}
                                        >
                                            Cari
                                        </button>
                                    </div>
                                </div>
                                
                                </div>
                                
                                {/* Member Info Card */}
                                {laporan.member && (
                                    <div className="bg-blue-50 border-l-4 border-blue-500 p-4 rounded-lg mt-4">
                                        <div className="flex items-center">
                                            <div className="flex-shrink-0">
                                                <svg className="h-5 w-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                </svg>
                                            </div>
                                            <div className="ml-3">
                                                <h3 className="text-sm font-medium text-blue-800">Data Anggota</h3>
                                                <div className="mt-1 grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-blue-700">
                                                    <div><span className="font-medium">Nama:</span> {laporan.member.name}</div>
                                                    <div><span className="font-medium">NIK:</span> {laporan.member.nik}</div>
                                                    <div><span className="font-medium">Bagian:</span> {laporan.member.bagian || '-'}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            
                            <div className="bg-white p-4 rounded-lg border border-gray-200 mt-6">
                                <div className="overflow-x-auto w-full" style={{ WebkitOverflowScrolling: 'touch' }}>
                                    <table className="min-w-max w-full border border-gray-200" style={{ tableLayout: 'auto' }}>
                                        <thead>
                                            <tr className="bg-gray-50">
                                                <th rowSpan="2" className="border px-4 py-2 whitespace-nowrap">Tanggal</th>
                                                <th rowSpan="2" className="border px-4 py-2 whitespace-nowrap">Keterangan</th>
                                                <th colSpan="6" className="border px-4 py-2 bg-blue-50">Simpanan</th>
                                                <th colSpan="3" className="border px-4 py-2 bg-green-50">Pinjaman</th>
                                                <th colSpan="3" className="border px-4 py-2 bg-yellow-50">Bunga Pinjaman</th>
                                            </tr>
                                            <tr className="bg-gray-50">
                                                {/* Sub-headers for Simpanan */}
                                                <th className="border px-2 py-1 bg-blue-50">Pokok</th>
                                                <th className="border px-2 py-1 bg-blue-50">Wajib</th>
                                                <th className="border px-2 py-1 bg-blue-50">Sukarela</th>
                                                <th className="border px-2 py-1 bg-blue-50">Sihara</th>
                                                <th className="border px-2 py-1 bg-blue-50">Deposito</th>
                                                <th className="border px-2 py-1 bg-blue-50">Total</th>
                                                
                                                {/* Sub-headers for Pinjaman */}
                                                <th className="border px-2 py-1 bg-green-50">Umum</th>
                                                <th className="border px-2 py-1 bg-green-50">Khusus</th>
                                                <th className="border px-2 py-1 bg-green-50">EMG</th>
                                                
                                                {/* Sub-headers for Bunga */}
                                                <th className="border px-2 py-1 bg-yellow-50">Umum</th>
                                                <th className="border px-2 py-1 bg-yellow-50">Khusus</th>
                                                <th className="border px-2 py-1 bg-yellow-50">EMG</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {laporan.transactions.length > 0 ? (
                                                laporan.transactions.map((item, index) => (
                                                    <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                                                        <td className="border px-4 py-2 text-center">
                                                            {format(parseISO(item.tanggal), 'dd/MM/yyyy')}
                                                        </td>
                                                        <td className="border px-4 py-2">{item.keterangan}</td>
                                                        
                                                        {/* Simpanan */}
                                                        <td className="border px-2 py-1">
                                                            <CurrencyFormat value={item.simpanan_pokok} className="block text-right w-full" />
                                                        </td>
                                                        <td className="border px-2 py-1">
                                                            <CurrencyFormat value={item.simpanan_wajib} className="block text-right w-full" />
                                                        </td>
                                                        <td className="border px-2 py-1">
                                                            <CurrencyFormat value={item.simpanan_sukarela} className="block text-right w-full" />
                                                        </td>
                                                        <td className="border px-2 py-1">
                                                            <CurrencyFormat value={item.simpanan_sihara} className="block text-right w-full" />
                                                        </td>
                                                        <td className="border px-2 py-1">
                                                            <CurrencyFormat value={item.simpanan_deposito} className="block text-right w-full" />
                                                        </td>
                                                        <td className="border px-2 py-1 font-medium bg-blue-50">
                                                            <CurrencyFormat 
                                                                value={(Number(item.simpanan_pokok) || 0) + 
                                                                       (Number(item.simpanan_wajib) || 0) + 
                                                                       (Number(item.simpanan_sukarela) || 0) + 
                                                                       (Number(item.simpanan_sihara) || 0) + 
                                                                       (Number(item.simpanan_deposito) || 0)} 
                                                                className="block text-right w-full" 
                                                            />
                                                        </td>
                                                        
                                                        {/* Pinjaman */}
                                                        <td className="border px-2 py-1">
                                                            <CurrencyFormat value={item.pinjaman_umum} className="block text-right w-full" />
                                                        </td>
                                                        <td className="border px-2 py-1">
                                                            <CurrencyFormat value={item.pinjaman_khusus} className="block text-right w-full" />
                                                        </td>
                                                        <td className="border px-2 py-1">
                                                            <CurrencyFormat value={item.pinjaman_emg} className="block text-right w-full" />
                                                        </td>
                                                        
                                                        {/* Bunga Pinjaman */}
                                                        <td className="border px-2 py-1">
                                                            <CurrencyFormat value={item.bunga_umum} className="block text-right w-full" />
                                                        </td>
                                                        <td className="border px-2 py-1">
                                                            <CurrencyFormat value={item.bunga_khusus} className="block text-right w-full" />
                                                        </td>
                                                        <td className="border px-2 py-1">
                                                            <CurrencyFormat value={item.bunga_emg} className="block text-right w-full" />
                                                        </td>
                                                        

                                                    </tr>
                                                ))
                                            ) : search ? (
                                                <tr>
                                                    <td colSpan="13" className="border px-4 py-4 text-center text-gray-500">
                                                        {laporan.member 
                                                            ? 'Tidak ada data transaksi untuk periode yang dipilkan'
                                                            : 'Data anggota tidak ditemukan'}
                                                    </td>
                                                </tr>
                                            ) : (
                                                <tr>
                                                    <td colSpan="13" className="border px-4 py-4 text-center text-gray-500">
                                                        Silakan cari anggota terlebih dahulu untuk melihat laporan
                                                    </td>
                                                </tr>
                                            )}
                                        </tbody>

                                        </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
