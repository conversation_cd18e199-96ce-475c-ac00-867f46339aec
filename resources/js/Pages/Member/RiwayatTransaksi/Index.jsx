import React, { useState, useEffect } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { format, parseISO } from 'date-fns';
import { id } from 'date-fns/locale';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import CurrencyFormat from '@/Components/CurrencyFormat';
import MonthFilterMember from '@/Components/MonthFilterMember';

const TransactionTypeBadge = ({ type }) => {
    const typeClasses = {
        simpanan: 'bg-blue-100 text-blue-800',
        pinjaman: 'bg-green-100 text-green-800',
        default: 'bg-gray-100 text-gray-800'
    };
    
    const displayType = type === 'simpanan' ? 'Simpanan' : type === 'pinjaman' ? 'Pinjaman' : type;
    
    return (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeClasses[type] || typeClasses.default}`}>
            {displayType}
        </span>
    );
};

const TransactionRow = React.memo(({ transaction, index, isMobile }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    
    const toggleExpand = () => {
        setIsExpanded(!isExpanded);
    };

    if (isMobile) {
        return (
            <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 mb-3 overflow-hidden">
                <div 
                    className="flex justify-between items-start cursor-pointer"
                    onClick={toggleExpand}
                >
                    <div className="flex-1">
                        <div className="font-medium text-gray-900">
                            {transaction.jenis_transaksi}
                        </div>
                        <div className="text-sm text-gray-500 mt-1">
                            {format(parseISO(transaction.tanggal), 'dd MMM yyyy', { locale: id })}
                        </div>
                    </div>
                    <div className="text-right ml-4">
                        <div className={`font-medium ${transaction.jumlah >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            <CurrencyFormat value={Math.abs(transaction.jumlah)} />
                        </div>
                        <div className="mt-1">
                            <span className="text-xs text-gray-500">
                                {isExpanded ? 'Sembunyikan' : 'Lihat detail'}
                            </span>
                        </div>
                    </div>
                </div>
                
                {/* Collapsible Content */}
                <div 
                    className={`transition-all duration-300 ease-in-out overflow-hidden ${isExpanded ? 'max-h-96 mt-3' : 'max-h-0'}`}
                >
                    <div className="pt-3 border-t border-gray-100">
                        <div className="grid grid-cols-2 gap-2 text-sm">
                            <div className="text-gray-500">Tipe:</div>
                            <div>
                                <TransactionTypeBadge type={transaction.tipe_transaksi} />
                            </div>
                            
                            <div className="text-gray-500">Kategori:</div>
                            <div>{transaction.kategori || '-'}</div>
                            
                            <div className="text-gray-500">Waktu:</div>
                            <div>{format(parseISO(transaction.tanggal), 'HH:mm', { locale: id })} WIB</div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Desktop View
    return (
        <>
            <tr 
                className="hover:bg-gray-50 cursor-pointer"
                onClick={toggleExpand}
            >
                <td className="px-4 py-4 whitespace-nowrap text-sm text-center text-gray-500">
                    {index + 1}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {format(parseISO(transaction.tanggal), 'dd MMM yyyy', { locale: id })}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {transaction.jenis_transaksi}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                    <TransactionTypeBadge type={transaction.tipe_transaksi} />
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {transaction.kategori || '-'}
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium text-right ${transaction.jumlah >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    <CurrencyFormat value={transaction.jumlah} />
                </td>
                <td className="px-6 py-4 text-sm text-gray-500">
                    <span className="text-indigo-600 hover:text-indigo-800">
                        {isExpanded ? 'Sembunyikan' : 'Lihat detail'}
                    </span>
                </td>
            </tr>
            
            {/* Expanded Row */}
            {isExpanded && (
                <tr className="bg-gray-50">
                    <td colSpan="7" className="px-6 py-4">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                            <div className="space-y-2">
                                <div className="font-medium">Detail Transaksi</div>
                                <div className="text-gray-500">
                                    <div className="mb-1">
                                        <span className="font-medium">Waktu: </span>
                                        {format(parseISO(transaction.tanggal), 'HH:mm', { locale: id })} WIB
                                    </div>
                                </div>
                            </div>
                            <div className="space-y-2">
                                <div className="font-medium">Informasi Lainnya</div>
                                <div className="text-gray-500">
                                    <div className="mb-1">
                                        <span className="font-medium">Status: </span>
                                        <span className="text-green-600">Berhasil</span>
                                    </div>
                                    <div>
                                        <span className="font-medium">No. Referensi: </span>
                                        {transaction.id}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            )}
        </>
    );
});

export default function RiwayatTransaksi({ auth, transactions: transactionsProp, filters }) {
    const transactions = transactionsProp?.data ? transactionsProp : { data: [] };
    
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;
    
    // Generate years list (5 years back and 1 year forward)
    const years = [];
    for (let year = currentYear - 5; year <= currentYear + 1; year++) {
        years.push(year);
    }

    const [sortConfig, setSortConfig] = useState({
        key: 'tanggal',
        direction: 'desc',
    });

    const [params, setParams] = useState(() => ({
        bulan: filters?.bulan || currentMonth,
        tahun: filters?.tahun || currentYear,
        search: filters?.search || '',
        type: filters?.type || 'all',
        sort: filters?.sort || 'tanggal',
        order: filters?.order || 'desc',
    }));
    
    const [selectedMonth, setSelectedMonth] = useState(() => filters?.bulan || currentMonth);
    const [selectedYear, setSelectedYear] = useState(() => filters?.tahun || currentYear);

    // Initialize sort config from filters
    useEffect(() => {   
        if (filters?.sort && filters?.order) {
            setSortConfig({
                key: filters.sort,
                direction: filters.order,
            });
        }
    }, [filters?.sort, filters?.order]);

    const handleSort = (key) => {
        const direction = sortConfig.key === key && sortConfig.direction === 'asc' ? 'desc' : 'asc';
        const newSortConfig = { key, direction };
        setSortConfig(newSortConfig);
        
        handleFilter({
            sort: key,
            order: direction,
            page: 1,
        });
    };

    // Helper function to get default dates
    const getDefaultDates = () => {
        const now = new Date();
        const currentMonth = now.getMonth() + 1;
        const currentYear = now.getFullYear();

        return getDateRangeFromMonthYear(currentMonth, currentYear);
    };

    // Helper function to convert month/year to start_date/end_date
    const getDateRangeFromMonthYear = (month, year) => {
        // Ensure we're working with numbers
        const monthNum = parseInt(month);
        const yearNum = parseInt(year);

        // Create dates in local timezone to avoid timezone issues
        const startOfMonth = new Date(yearNum, monthNum - 1, 1);
        const endOfMonth = new Date(yearNum, monthNum, 0); // Day 0 of next month = last day of current month

        // Format to YYYY-MM-DD
        const start_date = startOfMonth.getFullYear() + '-' +
                          String(startOfMonth.getMonth() + 1).padStart(2, '0') + '-' +
                          String(startOfMonth.getDate()).padStart(2, '0');

        const end_date = endOfMonth.getFullYear() + '-' +
                        String(endOfMonth.getMonth() + 1).padStart(2, '0') + '-' +
                        String(endOfMonth.getDate()).padStart(2, '0');



        return { start_date, end_date };
    };

    const handleFilter = (newParams = {}) => {
        const updatedParams = {
            ...params,
            ...newParams,
            bulan: newParams.bulan !== undefined ? newParams.bulan : selectedMonth,
            tahun: newParams.tahun !== undefined ? newParams.tahun : selectedYear,
            sort: newParams.sort !== undefined ? newParams.sort : sortConfig.key,
            order: newParams.order !== undefined ? newParams.order : sortConfig.direction,
        };

        // Update state
        setParams(prev => ({
            ...prev,
            ...updatedParams
        }));

        if (newParams.bulan !== undefined) setSelectedMonth(newParams.bulan);
        if (newParams.tahun !== undefined) setSelectedYear(newParams.tahun);

        // Convert month/year to start_date/end_date for backend
        const dateRange = getDateRangeFromMonthYear(updatedParams.bulan, updatedParams.tahun);

        // Clean up params before sending - use start_date/end_date instead of bulan/tahun
        const cleanParams = Object.fromEntries(
            Object.entries({
                ...updatedParams,
                start_date: newParams.start_date || dateRange.start_date,
                end_date: newParams.end_date || dateRange.end_date,
                page: newParams.page || updatedParams.page, // Include page parameter
                // Remove bulan/tahun as backend doesn't expect them
                bulan: undefined,
                tahun: undefined
            }).filter(([_, v]) => v !== undefined && v !== '' && v !== null)
        );

        // Only make the request if we have valid parameters
        if (Object.keys(cleanParams).length > 0) {
            router.get(route('member.riwayat.index'), cleanParams, {
                preserveState: true,
                preserveScroll: true,
                only: ['transactions', 'filters'],
            });
        }
    };
    
    const resetFilter = () => {
        const now = new Date();
        setSelectedMonth(now.getMonth() + 1);
        setSelectedYear(now.getFullYear());
    };

    const formatDate = (dateString) => {
        if (!dateString) return '-';
        const options = { day: '2-digit', month: 'short', year: 'numeric' };
        return new Date(dateString).toLocaleDateString('id-ID', options);
    };

    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth < 768);
        };

        // Set initial value
        handleResize();

        // Add event listener
        window.addEventListener('resize', handleResize);

        // Clean up
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    return (
        <AuthenticatedLayout
            user={auth.user}
            header={
                <div className="flex flex-col space-y-2">
                    <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                        Riwayat Transaksi
                    </h2>
                </div>
            }
        >
            <Head title="Riwayat Transaksi" />
            <div className="py-4">
                <div className="max-w-full mx-auto px-2 sm:px-4 lg:px-6">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-4 sm:p-5">
                            {/* Filter Section */}
                            <MonthFilterMember
                                title="Filter Riwayat Transaksi"
                                description="Filter berdasarkan periode dan tipe transaksi"
                                selectedMonth={selectedMonth}
                                selectedYear={selectedYear}
                                searchValue={params.search}
                                typeValue={params.type}
                                showTypeFilter={true}
                                onMonthChange={(month) => {
                                    setSelectedMonth(month);
                                    handleFilter({ bulan: month });
                                }}
                                onYearChange={(year) => {
                                    setSelectedYear(year);
                                    handleFilter({ tahun: year });
                                }}
                                onSearchChange={(e) => setParams(prev => ({ ...prev, search: e.target.value }))}
                                onSearchKeyDown={(e) => e.key === 'Enter' && handleFilter({ search: e.target.value })}
                                onTypeChange={(e) => handleFilter({ type: e.target.value })}
                                onApply={() => handleFilter()}
                                onReset={() => {
                                    const now = new Date();
                                    setSelectedMonth(now.getMonth() + 1);
                                    setSelectedYear(now.getFullYear());
                                    handleFilter({
                                        bulan: now.getMonth() + 1,
                                        tahun: now.getFullYear(),
                                        search: '',
                                        type: 'all',
                                        sort: 'tanggal',
                                        order: 'desc'
                                    });
                                }}
                            />

                            {/* Transactions */}
                            {transactions.data.length > 0 ? (
                                <>
                                    {/* Desktop Table */}
                                    <div className="hidden sm:block overflow-x-auto">
                                        <table className="min-w-full divide-y divide-gray-200">
                                            <thead className="bg-gray-50">
                                                <tr>
                                                    <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                                                        No
                                                    </th>
                                                    <th 
                                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                                        onClick={() => handleSort('tanggal')}
                                                    >
                                                        <div className="flex items-center">
                                                            Tanggal
                                                            {sortConfig.key === 'tanggal' && (
                                                                <span className="ml-1">
                                                                    {sortConfig.direction === 'asc' ? '↑' : '↓'}
                                                                </span>
                                                            )}
                                                        </div>
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Jenis
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Tipe
                                                    </th>
                                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Kategori
                                                    </th>
                                                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Jumlah
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody className="bg-white divide-y divide-gray-200">
                                                {transactions.data.map((transaction, index) => (
                                                    <TransactionRow 
                                                        key={`${transaction.id}-${index}`} 
                                                        transaction={transaction} 
                                                        index={transactions.from + index - 1}
                                                        isMobile={false}
                                                    />
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>

                                    {/* Mobile List */}
                                    <div className="sm:hidden space-y-3">
                                        {transactions.data.map((transaction, index) => (
                                            <TransactionRow 
                                                key={`${transaction.id}-${index}-mobile`} 
                                                transaction={transaction} 
                                                index={transactions.from + index - 1}
                                                isMobile={true}
                                            />
                                        ))}
                                    </div>

                                    {/* Pagination */}
                                    {transactions.last_page > 1 && (
                                        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                                            <div className="flex-1 flex justify-between sm:hidden">
                                                <button
                                                    onClick={() => handleFilter({ page: transactions.current_page - 1 })}
                                                    disabled={!transactions.prev_page_url}
                                                    className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                                                        !transactions.prev_page_url ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'
                                                    }`}
                                                >
                                                    Sebelumnya
                                                </button>
                                                <button
                                                    onClick={() => handleFilter({ page: transactions.current_page + 1 })}
                                                    disabled={!transactions.next_page_url}
                                                    className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                                                        !transactions.next_page_url ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'
                                                    }`}
                                                >
                                                    Selanjutnya
                                                </button>
                                            </div>
                                            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                                <div>
                                                    <p className="text-sm text-gray-700">
                                                        Menampilkan <span className="font-medium">{transactions.from || 0}</span> ke <span className="font-medium">{transactions.to || 0}</span> dari{' '}
                                                        <span className="font-medium">{transactions.total || 0}</span> hasil
                                                    </p>
                                                </div>
                                                <div>
                                                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                                        <button
                                                            onClick={() => handleFilter({ page: transactions.current_page - 1 })}
                                                            disabled={!transactions.prev_page_url}
                                                            className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-r-0 border-gray-300 bg-white text-sm font-medium ${
                                                                !transactions.prev_page_url ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                                                            }`}
                                                        >
                                                            <span className="sr-only">Previous</span>
                                                            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                                <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                                                            </svg>
                                                        </button>
                                                        {(() => {
                                                            const pages = [];
                                                            const totalPages = transactions.last_page;
                                                            const currentPage = transactions.current_page;
                                                            const maxVisiblePages = 5;

                                                            pages.push(1);

                                                            if (currentPage > maxVisiblePages - 1) {
                                                                pages.push('...');
                                                            }


                                                            let startPage = Math.max(2, currentPage - 1);
                                                            let endPage = Math.min(totalPages - 1, currentPage + 1);

                                                            if (currentPage <= maxVisiblePages - 1) {
                                                                endPage = maxVisiblePages;
                                                            }


                                                            if (currentPage >= totalPages - (maxVisiblePages - 2)) {
                                                                startPage = totalPages - (maxVisiblePages - 1);
                                                            }

                                                            for (let i = startPage; i <= endPage; i++) {
                                                                if (i > 1 && i < totalPages) {
                                                                    pages.push(i);
                                                                }
                                                            }

                                                            if (currentPage < totalPages - (maxVisiblePages - 2)) {
                                                                if (currentPage < totalPages - (maxVisiblePages - 1)) {
                                                                    pages.push('...');
                                                                }
                                                                pages.push(totalPages);
                                                            } else if (totalPages > 1) {
                                                                pages.push(totalPages);
                                                            }

                                                            return pages.map((page, index) => {
                                                                if (page === '...') {
                                                                    return (
                                                                        <span key={`ellipsis-${index}`} className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                                                            ...
                                                                        </span>
                                                                    );
                                                                }

                                                                return (
                                                                    <button
                                                                        key={page}
                                                                        onClick={() => handleFilter({ page })}
                                                                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                                                            transactions.current_page === page
                                                                                ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                                                                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                                                        }`}
                                                                    >
                                                                        {page}
                                                                    </button>
                                                                );
                                                            });
                                                        })()}
                                                        <button
                                                            onClick={() => handleFilter({ page: transactions.current_page + 1 })}
                                                            disabled={!transactions.next_page_url}
                                                            className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                                                                !transactions.next_page_url ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                                                            }`}
                                                        >
                                                            <span className="sr-only">Next</span>
                                                            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                                            </svg>
                                                        </button>
                                                    </nav>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </>
                            ) : (
                                <div className="text-center py-12">
                                    <svg
                                        className="mx-auto h-12 w-12 text-gray-400"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                        aria-hidden="true"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={1}
                                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                        />
                                    </svg>
                                    <h3 className="mt-2 text-sm font-medium text-gray-900">Tidak ada transaksi</h3>
                                    <p className="mt-1 text-sm text-gray-500">Tidak ada data transaksi yang ditemukan untuk filter yang dipilih.</p>
                                    <div className="mt-6">
                                        <button
                                            type="button"
                                            onClick={() => {
                                                const now = new Date();
                                                const defaultDates = getDefaultDates();
                                                setSelectedMonth(now.getMonth() + 1);
                                                setSelectedYear(now.getFullYear());
                                                handleFilter({
                                                    bulan: now.getMonth() + 1,
                                                    tahun: now.getFullYear(),
                                                    start_date: defaultDates.start_date,
                                                    end_date: defaultDates.end_date,
                                                    search: '',
                                                    type: 'all',
                                                    sort: 'tanggal',
                                                    order: 'desc'
                                                });
                                            }}
                                            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                        >
                                            Reset Filter
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}