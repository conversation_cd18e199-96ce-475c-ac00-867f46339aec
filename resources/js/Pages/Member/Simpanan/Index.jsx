import { useState, useEffect } from 'react';
import { router } from '@inertiajs/react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import MonthFilterMember from '@/Components/MonthFilterMember';
import CurrencyFormat from '@/Components/CurrencyFormat';
import SummaryCard from '@/Components/SummaryCard';


export default function SimpananSaya({ auth, errors, simpanan = {} }) {
    const { 
        data = [], 
        total_simpanan = 0, 
        total_setoran_bulan_ini = 0, 
        total_pengambilan_bulan_ini = 0,
        filter = {
            bulan: new Date().getMonth() + 1,
            tahun: new Date().getFullYear(),
            nama_bulan: '',
            tahun_list: []
        }
    } = simpanan;

    const [isMobile, setIsMobile] = useState(false);
    const [selectedMonth, setSelectedMonth] = useState(filter.bulan);
    const [selectedYear, setSelectedYear] = useState(filter.tahun);

    const handleFilterChange = () => {
        router.get(route('member.simpanan.index'), {
            bulan: selectedMonth,
            tahun: selectedYear
        }, {
            preserveState: true,
            preserveScroll: true,
            replace: true
        });
    };

    const resetFilter = () => {
        const now = new Date();
        setSelectedMonth(now.getMonth() + 1);
        setSelectedYear(now.getFullYear());
    };

    useEffect(() => {
        handleFilterChange();
    }, [selectedMonth, selectedYear]);

    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth < 640);
        };

        handleResize();

        window.addEventListener('resize', handleResize);

        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const formatRupiah = (amount, className = '') => {
        return <CurrencyFormat value={amount} className={className} />;
    };

    const columns = [
        {
            name: 'Jenis Simpanan',
            selector: row => row.jenis_simpanan,
            sortable: true,
            cell: row => <div className="text-left">{row.jenis_simpanan}</div>,
            minWidth: '150px',
        },
        {
            name: 'Setoran',
            selector: row => row.setoran,
            sortable: true,
            cell: row => <div className="text-right">{formatRupiah(row.setoran)}</div>,
            hide: isMobile ? 'sm' : undefined,
        },
        {
            name: 'Pengambilan',
            selector: row => row.pengambilan,
            sortable: true,
            cell: row => <div className="text-right">{formatRupiah(row.pengambilan)}</div>,
            hide: isMobile ? 'sm' : undefined,
        },
        {
            name: 'Saldo',
            selector: row => row.saldo,
            sortable: true,
            cell: row => <div className="text-right font-semibold">{formatRupiah(row.saldo)}</div>,
        },
    ];

    return (
        <AuthenticatedLayout header="Simpanan Saya">
            <div className="py-3 sm:py-6">
                <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6">
                    {/* Filter Section */}
                    <MonthFilterMember
                        title="Filter Laporan Simpanan"
                        description="Pilih bulan dan tahun untuk melihat data simpanan"
                        selectedMonth={selectedMonth}
                        selectedYear={selectedYear}
                        years={filter.tahun_list || []}
                        showTypeFilter={false}
                        showSearch={false}
                        onMonthChange={setSelectedMonth}
                        onYearChange={setSelectedYear}
                        onApply={handleFilterChange}
                        onReset={resetFilter}
                    />
                    <div className="mt-4 mb-4 px-3 py-2 bg-blue-50 rounded-md border border-blue-100">
                        <p className="text-sm text-blue-800">
                            <span className="font-medium">Periode: </span>
                            <span className="font-semibold">{filter.nama_bulan} {filter.tahun}</span>
                        </p>
                    </div>

                    {/* Summary Cards */}
                    <div className="grid grid-cols-1 gap-3 sm:gap-4 md:gap-5 sm:grid-cols-2 lg:grid-cols-3">
                        <SummaryCard 
                            title="Total Saldo Simpanan Bulan Ini"
                            value={formatRupiah(total_simpanan)}
                            color="green"
                            icon={
                                <svg className="h-5 w-5 sm:h-6 sm:w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            }
                        />
                        
                        <SummaryCard 
                            title="Total Pengambilan Bulan Ini"
                            value={formatRupiah(total_pengambilan_bulan_ini)}
                            color="red"
                            icon={
                                <svg className="h-5 w-5 sm:h-6 sm:w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            }
                        />
                        
                        <SummaryCard 
                            title="Total Setoran Bulan Ini"
                            value={formatRupiah(total_setoran_bulan_ini)}
                            color="green"
                            icon={
                                <svg className="h-5 w-5 sm:h-6 sm:w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                            }
                        />
                    </div>

                    {/* Manual Table */}
                    <div className="mt-4 sm:mt-6 md:mt-8 bg-white overflow-hidden shadow rounded-lg">
                        <div className="p-4">
                            <h2 className="text-lg font-semibold mb-3">Simpanan Bulan Ini</h2>
                            <div className="overflow-x-auto">
                                {data.length === 0 ? (
                                    <div className="p-4 text-center text-gray-500">
                                        Tidak ada data simpanan
                                    </div>
                                ) : (
                                    <div className="min-w-full">
                                        {/* Desktop Table */}
                                        <table className="min-w-full divide-y divide-gray-200 hidden sm:table">
                                            <thead className="bg-gray-50">
                                                <tr>
                                                    <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Jenis Simpanan
                                                    </th>
                                                    <th scope="col" className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Setoran
                                                    </th>
                                                    <th scope="col" className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Pengambilan
                                                    </th>
                                                    <th scope="col" className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Saldo
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody className="bg-white divide-y divide-gray-200">
                                                {data.map((item, index) => (
                                                    <tr key={index} className="hover:bg-gray-50">
                                                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                                            {item.jenis_simpanan}
                                                        </td>
                                                        <td className="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-900">
                                                            {formatRupiah(item.setoran)}
                                                        </td>
                                                        <td className="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-900">
                                                            {formatRupiah(item.pengambilan)}
                                                        </td>
                                                        <td className="px-3 py-4 whitespace-nowrap text-sm font-semibold text-right text-gray-900">
                                                            {formatRupiah(item.saldo)}
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>

                                        {/* Mobile Cards */}
                                        <div className="space-y-3 sm:hidden">
                                            {data.map((item, index) => (
                                                <div key={index} className="bg-white p-4 rounded-lg shadow border border-gray-100">
                                                    <div className="flex justify-between items-start">
                                                        <div>
                                                            <h3 className="text-sm font-medium text-gray-900">{item.jenis_simpanan}</h3>
                                                            <div className="mt-1 text-sm text-gray-500">
                                                                <p>Setoran: {formatRupiah(item.setoran)}</p>
                                                                <p>Pengambilan: {formatRupiah(item.pengambilan)}</p>
                                                            </div>
                                                        </div>
                                                        <div className="text-right">
                                                            <span className="text-sm font-semibold text-gray-900">
                                                                {formatRupiah(item.saldo)}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}