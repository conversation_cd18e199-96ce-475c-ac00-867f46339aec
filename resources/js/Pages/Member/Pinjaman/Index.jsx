import { useState, useEffect } from 'react';
import { router } from '@inertiajs/react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import MonthFilterMember from '@/Components/MonthFilterMember';
import CurrencyFormat from '@/Components/CurrencyFormat';
import SummaryCard from '@/Components/SummaryCard';

export default function PinjamanSaya({ pinjaman = [], summary = {}, filter = {}, jsp_per_jenis = {} }) {
    const [isMobile, setIsMobile] = useState(false);
    const [selectedMonth, setSelectedMonth] = useState(filter.bulan || new Date().getMonth() + 1);
    const [selectedYear, setSelectedYear] = useState(filter.tahun || new Date().getFullYear());

    const handleFilterChange = () => {
        router.get(route('member.pinjaman.index'), {
            bulan: selectedMonth,
            tahun: selectedYear
        }, {
            preserveState: true,
            preserveScroll: true,
            replace: true
        });
    };

    const resetFilter = () => {
        const now = new Date();
        setSelectedMonth(now.getMonth() + 1);
        setSelectedYear(now.getFullYear());
    };
    
    useEffect(() => {
        handleFilterChange();
    }, [selectedMonth, selectedYear]);
    
    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth < 640);
        };

        handleResize();

        window.addEventListener('resize', handleResize);

        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const processedPinjaman = pinjaman.map((item, index) => ({
        id: index + 1,
        jenis_pinjaman: item.jenis,
        jumlah_pinjaman: item.pinjaman_bulan_ini || 0,  // Use monthly loan amount
        pinjaman_bulan_ini: item.pinjaman_bulan_ini || 0,
        total_pembayaran: item.setoran_bulan_ini || 0,  // Use monthly payment amount
        pembayaran_bulan_ini: item.setoran_bulan_ini || 0,
        sisa_pinjaman: item.sisa_pinjaman || 0,
        status: item.sisa_pinjaman > 0 ? 'Belum Lunas' : 'Lunas'
    }));
    
    const totalPinjaman = summary.total_pinjaman_bulan_ini || 0;
    const totalPembayaran = summary.total_setoran_bulan_ini || 0;
    const totalSisaPinjaman = summary.total_sisa_pinjaman || 0;
    const totalJspUmumSaatIni = summary.total_jsp_umum_saat_ini || 0;

    // Use jsp_per_jenis from backend for JSP totals
    const jspTotals = {
        umum: jsp_per_jenis.umum || 0,
        khusus: jsp_per_jenis.khusus || 0,
        emergency: jsp_per_jenis.emergency || 0,
    };
    const totalJsp = Object.values(jspTotals).reduce((acc, value) => acc + value, 0);

    const formatRupiah = (amount, className = '') => {
        return <CurrencyFormat value={amount} className={className} />;
    };

    return (
        <AuthenticatedLayout header="Pinjaman Saya">
            <div className="py-3 sm:py-6">
                <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6">
                    {/* Filter Section */}
                    <MonthFilterMember
                        title="Filter Laporan Pinjaman"
                        description="Pilih bulan dan tahun untuk melihat data pinjaman"
                        selectedMonth={selectedMonth}
                        selectedYear={selectedYear}
                        years={filter.tahun_list || Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i)}
                        showTypeFilter={false}
                        showSearch={false}
                        onMonthChange={setSelectedMonth}
                        onYearChange={setSelectedYear}
                        onApply={handleFilterChange}
                        onReset={resetFilter}
                    />
                    <div className="mt-4 mb-4 px-3 py-2 bg-blue-50 rounded-md border border-blue-100">
                        <p className="text-sm text-blue-800">
                            <span className="font-medium">Periode: </span>
                            <span className="font-semibold">{filter.nama_bulan} {filter.tahun}</span>
                        </p>
                    </div>

                    {/* Summary Cards */}
                    <div className="grid grid-cols-1 gap-3 sm:gap-4 md:gap-5 sm:grid-cols-2 lg:grid-cols-3">
                        <SummaryCard 
                            title="Total Pinjaman Bulan Ini"
                            value={formatRupiah(totalPinjaman)}
                            color="blue"
                            icon={
                                <svg className="h-5 w-5 sm:h-6 sm:w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            }
                        />
                        
                        <SummaryCard 
                            title="Total Pembayaran Bulan Ini"
                            value={formatRupiah(totalPembayaran)}
                            color="green"
                            icon={
                                <svg className="h-5 w-5 sm:h-6 sm:w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            }
                        />
                        
                        <SummaryCard 
                            title="Sisa Pinjaman Bulan Ini"
                            value={formatRupiah(totalSisaPinjaman)}
                            color={totalSisaPinjaman > 0 ? 'red' : 'green'}
                            icon={
                                <svg className="h-5 w-5 sm:h-6 sm:w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            }
                        />

                        <SummaryCard 
                            title={`Akumulasi JSP Umum s.d. ${filter.nama_bulan}`}
                            value={formatRupiah(totalJspUmumSaatIni)}
                            color="purple"
                            icon={
                                <svg className="h-5 w-5 sm:h-6 sm:w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H9a2 2 0 00-2 2v2m8 0H7m10 0v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m10 0H7" />
                                </svg>
                            }
                        />
                    </div>

                    {/* JSP Table */}
                    {totalJsp > 0 && (
                        <div className="mt-8 bg-white overflow-hidden shadow rounded-lg">
                            <div className="p-4">
                                <h2 className="text-lg font-semibold mb-3">Setoran JSP Bulan Ini</h2>
                                {/* Desktop Table */}
                                <table className="min-w-full divide-y divide-gray-200 hidden sm:table">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Jenis Pinjaman</th>
                                            <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase">Total Setoran JSP</th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td className="px-3 py-3">Umum</td>
                                            <td className="px-3 py-3 text-right font-semibold">
                                                <CurrencyFormat value={jspTotals.umum} />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td className="px-3 py-3">Khusus</td>
                                            <td className="px-3 py-3 text-right font-semibold">
                                                <CurrencyFormat value={jspTotals.khusus} />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td className="px-3 py-3">Emergency</td>
                                            <td className="px-3 py-3 text-right font-semibold">
                                                <CurrencyFormat value={jspTotals.emergency} />
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                {/* Mobile Cards */}
                                <div className="space-y-3 sm:hidden">
                                    <div className="bg-white p-4 rounded-lg shadow border border-gray-100">
                                        <div className="flex justify-between items-center">
                                            <div>
                                                <h3 className="text-sm font-medium text-gray-900">Umum</h3>
                                            </div>
                                            <div className="text-right">
                                                <span className="text-sm font-semibold text-gray-900 block">
                                                    <CurrencyFormat value={jspTotals.umum} />
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="bg-white p-4 rounded-lg shadow border border-gray-100">
                                        <div className="flex justify-between items-center">
                                            <div>
                                                <h3 className="text-sm font-medium text-gray-900">Khusus</h3>
                                            </div>
                                            <div className="text-right">
                                                <span className="text-sm font-semibold text-gray-900 block">
                                                    <CurrencyFormat value={jspTotals.khusus} />
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="bg-white p-4 rounded-lg shadow border border-gray-100">
                                        <div className="flex justify-between items-center">
                                            <div>
                                                <h3 className="text-sm font-medium text-gray-900">Emergency</h3>
                                            </div>
                                            <div className="text-right">
                                                <span className="text-sm font-semibold text-gray-900 block">
                                                    <CurrencyFormat value={jspTotals.emergency} />
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Manual Table */}
                    <div className="mt-4 sm:mt-6 md:mt-8 bg-white overflow-hidden shadow rounded-lg">
                        <div className="p-4">
                            <h2 className="text-lg font-semibold mb-3">Pinjaman Bulan Ini</h2>
                            <div className="overflow-x-auto">
                                {processedPinjaman.length === 0 ? (
                                    <div className="p-4 text-center text-gray-500">
                                        Tidak ada data pinjaman
                                    </div>
                                ) : (
                                    <div className="min-w-full">
                                        {/* Desktop Table */}
                                        <table className="min-w-full divide-y divide-gray-200 hidden sm:table">
                                            <thead className="bg-gray-50">
                                                <tr>
                                                    <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Jenis Pinjaman
                                                    </th>
                                                    <th scope="col" className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Jumlah Pinjaman
                                                    </th>
                                                    <th scope="col" className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Total Pembayaran
                                                    </th>
                                                    <th scope="col" className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Sisa Pinjaman
                                                    </th>
                                                    <th scope="col" className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Status
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody className="bg-white divide-y divide-gray-200">
                                                {processedPinjaman.map((item) => (
                                                    <tr key={item.id} className="hover:bg-gray-50">
                                                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                                            {item.jenis_pinjaman}
                                                        </td>
                                                        <td className="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-900">
                                                            {formatRupiah(item.jumlah_pinjaman)}
                                                        </td>
                                                        <td className="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-900">
                                                            {formatRupiah(item.total_pembayaran)}
                                                        </td>
                                                        <td className="px-3 py-4 whitespace-nowrap text-sm font-semibold text-right text-gray-900">
                                                            {formatRupiah(item.sisa_pinjaman)}
                                                        </td>
                                                        <td className="px-2 py-4 whitespace-nowrap">
                                                            <div className="flex justify-center">
                                                                <span className={`px-2 py-1 inline-flex text-xs leading-none font-semibold rounded-full whitespace-nowrap ${
                                                                    item.sisa_pinjaman <= 0 
                                                                        ? 'bg-green-100 text-green-800' 
                                                                        : 'bg-yellow-100 text-yellow-800'
                                                                }`}>
                                                                    {item.sisa_pinjaman <= 0 ? 'Lunas' : 'Belum Lunas'}
                                                                </span>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>

                                        {/* Mobile Cards */}
                                        <div className="space-y-3 sm:hidden">
                                            {processedPinjaman.map((item) => (
                                                <div key={item.id} className="bg-white p-4 rounded-lg shadow border border-gray-100">
                                                    <div className="flex justify-between items-start">
                                                        <div>
                                                            <h3 className="text-sm font-medium text-gray-900">{item.jenis_pinjaman}</h3>
                                                            <div className="mt-1 text-sm text-gray-500 space-y-1">
                                                                <p>Pinjaman: {formatRupiah(item.pinjaman_bulan_ini)}</p>
                                                                <p>Dibayar: {formatRupiah(item.pembayaran_bulan_ini)}</p>
                                                            </div>
                                                        </div>
                                                        <div className="text-right">
                                                            <span className="text-sm font-semibold text-gray-900 block">
                                                                {formatRupiah(item.sisa_pinjaman)}
                                                            </span>
                                                            <span className={`mt-1 px-2 py-1 inline-flex text-xs leading-none font-semibold rounded-full whitespace-nowrap ${
                                                                item.sisa_pinjaman <= 0 
                                                                    ? 'bg-green-100 text-green-800' 
                                                                    : 'bg-yellow-100 text-yellow-800'
                                                            }`}>
                                                                {item.sisa_pinjaman <= 0 ? 'Lunas' : 'Belum Lunas'}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}