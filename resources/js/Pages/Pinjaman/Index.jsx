import React, { useState, useEffect } from 'react';
import { router } from '@inertiajs/react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import MonthFilter from '@/Components/MonthFilter';
import CurrencyFormat from '@/Components/CurrencyFormat';
import { format } from 'date-fns';

export default function Pinjaman({ pinjaman = { data: [] }, auth = {} }) {
    const [selectedMonth, setSelectedMonth] = useState(format(new Date(), 'yyyy-MM'));
    const [filteredData, setFilteredData] = useState(pinjaman?.data || []);

    useEffect(() => {
        // Filter data berdasarkan bulan yang dipilih
        router.get(route('admin.pinjaman.index'), { month: selectedMonth }, {
            preserveState: true,
            only: ['pinjaman'],
            onSuccess: (page) => {
                const pinjamanData = page.props.pinjaman?.data || [];
                setFilteredData(pinjamanData);
            },
            onError: () => {
                setFilteredData([]);
            }
        });
    }, [selectedMonth]);

    return (
        <AuthenticatedLayout
            user={auth?.user}
            header={
                <div className="font-semibold text-xl text-gray-800 leading-tight">
                    Data Pinjaman <span className="text-red-600">(DALAM PENGEMBANGAN)</span>
                </div>
            }
        >
            <div className="py-4">
                <div className="max-w-full mx-auto px-2 sm:px-4 lg:px-6">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-4 sm:p-5">
                            <MonthFilter 
                                selectedMonth={selectedMonth}
                                onMonthChange={(e) => setSelectedMonth(e.target.value)}
                                title="Filter Data Pinjaman"
                                description="Pilih periode untuk melihat data pinjaman"
                            />
                            
                            <div className="overflow-x-auto w-full mt-4" style={{ WebkitOverflowScrolling: 'touch' }}>
                                <table className="min-w-max w-full bg-white border border-gray-200 text-sm" style={{ tableLayout: 'auto' }}>
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th rowSpan="2" className="border px-4 py-2 text-center whitespace-nowrap">NO</th>
                                            <th rowSpan="2" className="border px-4 py-2 text-left whitespace-nowrap">NIK</th>
                                            <th rowSpan="2" className="border px-4 py-2 text-left whitespace-nowrap">Nama</th>
                                            <th rowSpan="2" className="border px-4 py-2 text-left whitespace-nowrap">Bagian</th>
                                            <th rowSpan="2" className="border px-4 py-2 text-right whitespace-nowrap">Saldo Awal</th>
                                            
                                            <th colSpan="3" className="border text-center bg-blue-50">PINJAMAN</th>
                                            <th colSpan="6" className="border text-center bg-green-50">PEMASUKAN</th>
                                            <th rowSpan="2" className="border px-4 py-2 text-right bg-yellow-50 whitespace-nowrap">Total</th>
                                        </tr>
                                        <tr>
                                            {/* Subkolom Pinjaman */}
                                            <th className="border px-4 py-2 text-right bg-blue-50">Umum</th>
                                            <th className="border px-4 py-2 text-right bg-blue-50">Khusus</th>
                                            <th className="border px-4 py-2 text-right bg-blue-50">Emergency</th>
                                            
                                            {/* Subkolom Pemasukan */}
                                            <th className="border px-4 py-2 text-right bg-green-50">Umum</th>
                                            <th className="border px-4 py-2 text-right bg-green-50">Khusus</th>
                                            <th className="border px-4 py-2 text-right bg-green-50">Emergency</th>
                                            <th className="border px-4 py-2 text-right bg-green-100">JSP Umum</th>
                                            <th className="border px-4 py-2 text-right bg-green-100">JSP Khusus</th>
                                            <th className="border px-4 py-2 text-right bg-green-100">JSP Emergency</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {filteredData.map((item, index) => (
                                            <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}> 
                                                <td className="border px-2 py-2 text-center whitespace-nowrap">{index + 1}</td>
                                                <td className="border px-4 py-2 whitespace-nowrap">{item.nik}</td>
                                                <td className="border px-4 py-2 whitespace-nowrap">{item.name}</td>
                                                <td className="border px-4 py-2 whitespace-nowrap">{item.bagian}</td>
                                                
                                                {/* Saldo Awal */}
                                                <td className="border px-4 py-2 text-right">
                                                    <CurrencyFormat value={item.saldo_awal} className="block text-right w-full" />
                                                </td>
                                                
                                                {/* Pinjaman */}
                                                <td className="border px-4 py-2 text-right bg-blue-50">
                                                    <CurrencyFormat value={item.pinjaman_umum} className="block text-right w-full" />
                                                </td>
                                                <td className="border px-4 py-2 text-right bg-blue-50">
                                                    <CurrencyFormat value={item.pinjaman_khusus} className="block text-right w-full" />
                                                </td>
                                                <td className="border px-4 py-2 text-right bg-blue-50">
                                                    <CurrencyFormat value={item.pinjaman_emergency} className="block text-right w-full" />
                                                </td>
                                                
                                                {/* Pemasukan - Pinjaman */}
                                                <td className="border px-4 py-2 text-right">
                                                    <CurrencyFormat value={item.pemasukan_umum} className="block text-right w-full" />
                                                </td>
                                                <td className="border px-4 py-2 text-right">
                                                    <CurrencyFormat value={item.pemasukan_khusus} className="block text-right w-full" />
                                                </td>
                                                <td className="border px-4 py-2 text-right">
                                                    <CurrencyFormat value={item.pemasukan_emergency} className="block text-right w-full" />
                                                </td>
                                                
                                                {/* Pemasukan - Jasa Pinjaman (JSP) */}
                                                <td className="border px-4 py-2 text-right bg-green-50">
                                                    <CurrencyFormat value={item.jsp_umum} className="block text-right w-full" />
                                                </td>
                                                <td className="border px-4 py-2 text-right bg-green-50">
                                                    <CurrencyFormat value={item.jsp_khusus} className="block text-right w-full" />
                                                </td>
                                                <td className="border px-4 py-2 text-right bg-green-50">
                                                    <CurrencyFormat value={item.jsp_emergency} className="block text-right w-full" />
                                                </td>
                                                
                                                {/* Total */}
                                                <td className="border px-4 py-2 text-right font-semibold bg-yellow-50">
                                                    <CurrencyFormat 
                                                        value={
                                                            (Number(item.saldo_awal) || 0) + 
                                                            (Number(item.pinjaman_umum) || 0) + 
                                                            (Number(item.pinjaman_khusus) || 0) + 
                                                            (Number(item.pinjaman_emergency) || 0) -
                                                            (Number(item.pemasukan_umum) || 0) -
                                                            (Number(item.pemasukan_khusus) || 0) -
                                                            (Number(item.pemasukan_emergency) || 0) +
                                                            (Number(item.jsp_umum) || 0) +
                                                            (Number(item.jsp_khusus) || 0) +
                                                            (Number(item.jsp_emergency) || 0)
                                                        } 
                                                        className="block text-right w-full" 
                                                    />
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}