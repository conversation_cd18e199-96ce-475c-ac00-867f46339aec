import InputError from '@/Components/InputError';
import { useForm } from '@inertiajs/react';
import { useState } from 'react';
import { FaEnvelope, FaLock, FaEye, FaEyeSlash } from 'react-icons/fa';

export default function Login({ status, canResetPassword }) {
    const [showPassword, setShowPassword] = useState(false);
    const [role, setRole] = useState('admin');
    
    const { data, setData, post, processing, errors, reset } = useForm({
        email: '',
        password: '',
        role: 'admin',
    });

    const submit = (e) => {
        e.preventDefault();

        post(route('login'), {
            role: data.role,
            onSuccess: () => reset('password'),
            onError: (errors) => {
                if (errors.role) {
                    // Handle role validation error if needed
                    console.error(errors.role);
                }
            },
        });
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
            
            <div className="w-full max-w-md bg-white rounded-lg shadow-md overflow-hidden">
                {/* Header */}
                <div className="bg-blue-600 p-6 text-center">
                    <h1 className="text-2xl font-bold text-white">Koperasi Hegar Mulya</h1>
                    <p className="text-blue-100 text-sm mt-1">Silakan masuk ke akun Anda</p>
                </div>

                {/* Login Form */}
                <div className="p-6 md:p-8">
                    {status && (
                        <div className="mb-4 p-3 bg-green-100 text-green-700 text-sm rounded-md">
                            {status}
                        </div>
                    )}

                    <form onSubmit={submit} className="space-y-5">
                        {/* Role Selection */}
                        <div className="flex space-x-3 mb-4">
                            <button
                                type="button"
                                onClick={() => {
                                    setRole('admin');
                                    setData('role', 'admin');
                                }}
                                className={`flex-1 py-2 px-4 rounded-md font-medium text-sm ${
                                    role === 'admin' 
                                        ? 'bg-blue-600 text-white' 
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                }`}
                            >
                                Admin
                            </button>
                            <button
                                type="button"
                                onClick={() => {
                                    setRole('anggota');
                                    setData('role', 'anggota');
                                }}
                                className={`flex-1 py-2 px-4 rounded-md font-medium text-sm ${
                                    role === 'anggota' 
                                        ? 'bg-blue-600 text-white' 
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                }`}
                            >
                                Anggota
                            </button>
                        </div>

                        {/* Email Input */}
                        <div>
                            <label htmlFor="email" className="block text-sm text-gray-600 mb-1">Email</label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <FaEnvelope className="text-gray-400 text-sm" />
                                </div>
                                <input
                                    id="email"
                                    type="email"
                                    name="email"
                                    value={data.email}
                                    className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="<EMAIL>"
                                    autoComplete="email"
                                    onChange={(e) => setData('email', e.target.value)}
                                    required
                                />
                            </div>
                            <InputError message={errors.email} className="mt-1 text-xs" />
                        </div>

                        {/* Password Input */}
                        <div>
                            <div className="flex justify-between items-center mb-1">
                                <label htmlFor="password" className="block text-sm text-gray-600">Password</label>
                                {/* {canResetPassword && (
                                    <Link href={route('password.request')} className="text-xs text-blue-600 hover:underline">
                                        Lupa password?
                                    </Link>
                                )} */}
                            </div>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <FaLock className="text-gray-400 text-sm" />
                                </div>
                                <input
                                    id="password"
                                    type={showPassword ? "text" : "password"}
                                    name="password"
                                    value={data.password}
                                    className="block w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="••••••••"
                                    autoComplete="current-password"
                                    onChange={(e) => setData('password', e.target.value)}
                                    required
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowPassword(!showPassword)}
                                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                                >
                                    {showPassword ? <FaEyeSlash size={14} /> : <FaEye size={14} />}
                                </button>
                            </div>
                            <InputError message={errors.password} className="mt-1 text-xs" />
                        </div>

                        {/* Login Button */}
                        <div className="pt-2">
                            <button
                                type="submit"
                                disabled={processing}
                                className="w-full py-2.5 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md text-sm transition-colors disabled:opacity-70"
                            >
                                {processing ? 'Memproses...' : 'Masuk'}
                            </button>
                        </div>
                    </form>
                </div>
                
                {/* Footer */}
                <div className="bg-gray-50 p-4 text-center border-t border-gray-100">
                    <p className="text-xs text-gray-500">
                        &copy; {new Date().getFullYear()} Koperasi Hegar Mulya
                    </p>
                </div>
            </div>
        </div>
    );
}
