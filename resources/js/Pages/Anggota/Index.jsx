import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head, Link, router } from '@inertiajs/react';
import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import CreateAnggota from './Create';
import EditAnggota from './Edit';
import { Pencil, Trash2, ChevronDown, Loader2, Search } from 'lucide-react';
import { Dialog } from '@headlessui/react';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

export default function Anggota({ users, filters = {} }) {
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const [selectedUserId, setSelectedUserId] = useState(null);
    const [selectedUser, setSelectedUser] = useState(null);
    const [statusFilter, setStatusFilter] = useState(filters.status || 'all');
    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [isDeleting, setIsDeleting] = useState(false);

    // Debounce search
    useEffect(() => {
        const timer = setTimeout(() => {
            handleSearch();
        }, 500);
        return () => clearTimeout(timer);
    }, [searchQuery]);

    const handleSearch = () => {
        const params = new URLSearchParams(window.location.search);
        
        if (searchQuery) {
            params.set('search', searchQuery);
        } else {
            params.delete('search');
        }
        params.set('page', 1);
        
        router.get(`${window.location.pathname}?${params.toString()}`, {}, {
            preserveState: true,
            replace: true,
            only: ['users']
        });
    };

    const handleStatusChange = (status) => {
        setStatusFilter(status);
        const url = new URL(window.location);
        if (status === 'all') {
            url.searchParams.delete('status');
        } else {
            url.searchParams.set('status', status);
        }
        url.searchParams.set('page', 1);
        window.location = url;
    };

    const formatDate = (dateString) => {
        if (!dateString) return '-';
        return format(new Date(dateString), 'dd MMM yyyy', { locale: id });
    };

    const handleEdit = (user) => {
        setSelectedUser(user);
        setShowEditModal(true);
    };

    const openDeleteDialog = (id) => {
        setSelectedUserId(id);
        setShowDeleteDialog(true);
    };

    const closeDeleteDialog = () => {
        setShowDeleteDialog(false);
        setSelectedUserId(null);
    };

    const handleDelete = async () => {
        if (!selectedUserId) return;

        try {
            setIsDeleting(true);
            await router.delete(route('admin.anggota.destroy', selectedUserId), {
                preserveScroll: true,
                onSuccess: () => {
                    closeDeleteDialog();
                    toast.success('Data anggota berhasil dihapus');
                },
                onError: () => {
                    toast.error('Gagal menghapus data anggota');
                },
            });
        } finally {
            setIsDeleting(false);
        }
    };

    const columns = [
        {
            name: 'NIK',
            selector: (row) => row.nik,
            sortable: true,
        },
        {
            name: 'Nama',
            selector: (row) => row.name,
            sortable: true,
        },
        {
            name: 'Bagian',
            selector: (row) => row.bagian || '-',
            sortable: true,
        },
        {
            name: 'Email',
            selector: (row) => row.email,
            sortable: true,
        },
        {
            name: 'Status',
            cell: (row) => (
                <span
                    className={`px-2 py-1 rounded-full text-xs ${
                        row.status === 'Aktif'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                    }`}
                >
                    {row.status}
                </span>
            ),
            sortable: true,
        },
        {
            name: 'Tgl Masuk',
            selector: (row) => formatDate(row.tanggal_masuk),
            sortable: true,
        },
        {
            name: 'Tgl Keluar',
            selector: (row) => formatDate(row.tanggal_keluar),
            sortable: true,
        },
        {
            name: 'Alasan Keluar',
            selector: (row) => row.alasan_keluar || '-',
            wrap: true,
        },
        {
            name: 'Aksi',
            cell: (row) => (
                <div className="flex space-x-2">
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            handleEdit(row);
                        }}
                        className="text-blue-500 hover:text-blue-700 p-1"
                        title="Edit"
                    >
                        <Pencil className="h-4 w-4" />
                    </button>
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            openDeleteDialog(row.id);
                        }}
                        disabled={isDeleting}
                        className="p-1 text-red-500 hover:text-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Hapus"
                    >
                        {isDeleting ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                            <Trash2 className="h-4 w-4" />
                        )}
                    </button>
                </div>
            ),
            minWidth: '100px',
            maxWidth: '100px',
        },
    ];

    return (
        <AuthenticatedLayout header="Daftar Anggota">
            <Head title="Daftar Anggota" />

            <div className="py-4">
                <div className="max-w-full mx-auto px-2 sm:px-4 lg:px-6">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-4 sm:p-5">
                            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
                                <h2 className="text-lg font-medium text-gray-900">Daftar Anggota Koperasi</h2>
                                <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                                    <div className="relative w-full sm:w-64">
                                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <Search className="h-5 w-5 text-gray-400" />
                                        </div>
                                        <input
                                            type="text"
                                            value={searchQuery}
                                            onChange={(e) => setSearchQuery(e.target.value)}
                                            placeholder="Cari NIK, Nama, atau Email..."
                                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                    <div className="relative">
                                        <div className="relative">
                                            <select
                                                value={statusFilter}
                                                onChange={(e) => handleStatusChange(e.target.value)}
                                                className="appearance-none block w-full sm:w-48 pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white"
                                            >
                                                <option value="all">Semua Status</option>
                                                <option value="Aktif">Aktif</option>
                                                <option value="Tidak Aktif">Tidak Aktif</option>
                                            </select>
                                            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400">
                                                <ChevronDown className="h-4 w-4" />
                                            </div>
                                        </div>
                                    </div>
                                    <button
                                        onClick={() => setShowCreateModal(true)}
                                        className="inline-flex justify-center items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-900 focus:outline-none focus:border-blue-900 focus:ring focus:ring-blue-300 disabled:opacity-25 transition whitespace-nowrap"
                                    >
                                        Tambah Anggota
                                    </button>
                                </div>
                            </div>
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th
                                                scope="col"
                                                className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-12"
                                            >
                                                No
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                NIK
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Nama
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Bagian
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Email
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Status
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Tgl Masuk
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Tgl Keluar
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Alasan Keluar
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Aksi
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {users.data.length > 0 ? (
                                            users.data.map((user, index) => (
                                                <tr key={user.id} className="hover:bg-gray-50">
                                                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                                        {(users.current_page - 1) * users.per_page + index + 1}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.nik}</td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.name}</td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {user.bagian || '-'}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.email}</td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span
                                                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                                user.status === 'Aktif'
                                                                    ? 'bg-green-100 text-green-800'
                                                                    : 'bg-red-100 text-red-800'
                                                            }`}
                                                        >
                                                            {user.status}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {formatDate(user.tanggal_masuk)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {formatDate(user.tanggal_keluar)}
                                                    </td>
                                                    <td className="px-6 py-4 text-sm text-gray-500">{user.alasan_keluar || '-'}</td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                        <div className="flex justify-end space-x-2">
                                                            <button
                                                                onClick={() => handleEdit(user)}
                                                                className="text-blue-600 hover:text-blue-900"
                                                                title="Edit"
                                                            >
                                                                <Pencil className="h-4 w-4" />
                                                            </button>
                                                            <button
                                                                onClick={() => openDeleteDialog(user.id)}
                                                                disabled={isDeleting}
                                                                className="text-red-600 hover:text-red-900 disabled:opacity-50 disabled:cursor-not-allowed"
                                                                title="Hapus"
                                                            >
                                                                {isDeleting && selectedUserId === user.id ? (
                                                                    <Loader2 className="h-4 w-4 animate-spin" />
                                                                ) : (
                                                                    <Trash2 className="h-4 w-4" />
                                                                )}
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))
                                        ) : (
                                            <tr>
                                                <td colSpan="10" className="px-6 py-4 text-center text-sm text-gray-500">
                                                    Tidak ada data yang tersedia
                                                </td>
                                            </tr>
                                        )}
                                    </tbody>
                                </table>
                            </div>

                            {/* Pagination */}
                            {users.last_page > 1 && (
                            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                                <div className="flex-1 flex justify-between sm:hidden">
                                    <button
                                        onClick={() => {
                                            if (users.prev_page_url) {
                                                router.get(
                                                    `${window.location.pathname}?${new URLSearchParams({
                                                        ...Object.fromEntries(new URLSearchParams(window.location.search).entries()),
                                                        page: users.current_page - 1,
                                                    }).toString()}`,
                                                    {},
                                                    { preserveState: true, replace: true }
                                                );
                                            }
                                        }}
                                        className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                                            !users.prev_page_url
                                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                                : 'bg-white text-gray-700 hover:bg-gray-50'
                                        }`}
                                        disabled={!users.prev_page_url}
                                    >
                                        Sebelumnya
                                    </button>
                                    <button
                                        onClick={() => {
                                            if (users.next_page_url) {
                                                router.get(
                                                    `${window.location.pathname}?${new URLSearchParams({
                                                        ...Object.fromEntries(new URLSearchParams(window.location.search).entries()),
                                                        page: users.current_page + 1,
                                                    }).toString()}`,
                                                    {},
                                                    { preserveState: true, replace: true }
                                                );
                                            }
                                        }}
                                        className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                                            !users.next_page_url
                                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                                : 'bg-white text-gray-700 hover:bg-gray-50'
                                        }`}
                                        disabled={!users.next_page_url}
                                    >
                                        Selanjutnya
                                    </button>
                                </div>
                                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                    <div>
                                        <p className="text-sm text-gray-700">
                                            Menampilkan <span className="font-medium">{users.from}</span> ke{' '}
                                            <span className="font-medium">{users.to}</span> dari{' '}
                                            <span className="font-medium">{users.total}</span> hasil
                                        </p>
                                    </div>
                                    <div>
                                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                            <button
                                                onClick={() => {
                                                    if (users.prev_page_url) {
                                                        router.get(
                                                            `${window.location.pathname}?${new URLSearchParams({
                                                                ...Object.fromEntries(new URLSearchParams(window.location.search).entries()),
                                                                page: users.current_page - 1,
                                                            }).toString()}`,
                                                            {},
                                                            { preserveState: true, replace: true }
                                                        );
                                                    }
                                                }}
                                                className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                                                    !users.prev_page_url
                                                        ? 'text-gray-300 cursor-not-allowed'
                                                        : 'text-gray-500 hover:bg-gray-50'
                                                }`}
                                                disabled={!users.prev_page_url}
                                            >
                                                <span className="sr-only">Previous</span>
                                                <svg
                                                    className="h-5 w-5"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 0 20 20"
                                                    fill="currentColor"
                                                    aria-hidden="true"
                                                >
                                                    <path
                                                        fillRule="evenodd"
                                                        d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                                                        clipRule="evenodd"
                                                    />
                                                </svg>
                                            </button>
                                            {(() => {
                                                const pages = [];
                                                const totalPages = users.last_page;
                                                const currentPage = users.current_page;
                                                const maxVisiblePages = 5;

                                                pages.push(1);

                                                if (currentPage > maxVisiblePages - 1) {
                                                    pages.push('...');
                                                }

                                                let startPage = Math.max(2, currentPage - 1);
                                                let endPage = Math.min(totalPages - 1, currentPage + 1);

                                                if (currentPage <= maxVisiblePages - 1) {
                                                    endPage = maxVisiblePages;
                                                }

                                                if (currentPage >= totalPages - (maxVisiblePages - 2)) {
                                                    startPage = totalPages - (maxVisiblePages - 1);
                                                }

                                                for (let i = startPage; i <= endPage; i++) {
                                                    if (i > 1 && i < totalPages) {
                                                        pages.push(i);
                                                    }
                                                }

                                                if (currentPage < totalPages - (maxVisiblePages - 2)) {
                                                    if (currentPage < totalPages - (maxVisiblePages - 1)) {
                                                        pages.push('...');
                                                    }
                                                    pages.push(totalPages);
                                                } else if (totalPages > 1) {
                                                    pages.push(totalPages);
                                                }

                                                return pages.map((page, index) => {
                                                    if (page === '...') {
                                                        return (
                                                            <span
                                                                key={`ellipsis-${index}`}
                                                                className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                                                            >
                                                                ...
                                                            </span>
                                                        );
                                                    }

                                                    return (
                                                        <button
                                                            key={page}
                                                            onClick={() => {
                                                                router.get(
                                                                    `${window.location.pathname}?${new URLSearchParams({
                                                                        ...Object.fromEntries(new URLSearchParams(window.location.search).entries()),
                                                                        page,
                                                                    }).toString()}`,
                                                                    {},
                                                                    { preserveState: true, replace: true }
                                                                );
                                                            }}
                                                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                                                users.current_page === page
                                                                    ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                                                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                                            }`}
                                                        >
                                                            {page}
                                                        </button>
                                                    );
                                                });
                                            })()}
                                            <button
                                                onClick={() => {
                                                    if (users.next_page_url) {
                                                        router.get(
                                                            `${window.location.pathname}?${new URLSearchParams({
                                                                ...Object.fromEntries(new URLSearchParams(window.location.search).entries()),
                                                                page: users.current_page + 1,
                                                            }).toString()}`,
                                                            {},
                                                            { preserveState: true, replace: true }
                                                        );
                                                    }
                                                }}
                                                className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                                                    !users.next_page_url
                                                        ? 'text-gray-300 cursor-not-allowed'
                                                        : 'text-gray-500 hover:bg-gray-50'
                                                }`}
                                                disabled={!users.next_page_url}
                                            >
                                                <span className="sr-only">Next</span>
                                                <svg
                                                    className="h-5 w-5"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 0 20 20"
                                                    fill="currentColor"
                                                    aria-hidden="true"
                                                >
                                                    <path
                                                        fillRule="evenodd"
                                                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                                        clipRule="evenodd"
                                                    />
                                                </svg>
                                            </button>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        )}

                            {/* Delete Confirmation Dialog */}
                            <Dialog open={showDeleteDialog} onClose={closeDeleteDialog} className="relative z-50">
                                <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
                                <div className="fixed inset-0 flex items-center justify-center p-4">
                                    <Dialog.Panel className="mx-auto max-w-sm rounded bg-white p-6">
                                        <Dialog.Title className="text-lg font-medium text-gray-900 mb-4">
                                            Hapus Anggota
                                        </Dialog.Title>
                                        <Dialog.Description className="text-gray-600 mb-6">
                                            Apakah Anda yakin ingin menghapus anggota ini? Tindakan ini tidak dapat dibatalkan.
                                        </Dialog.Description>
                                        <div className="flex justify-end space-x-3">
                                            <button
                                                onClick={closeDeleteDialog}
                                                disabled={isDeleting}
                                                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                                            >
                                                Batal
                                            </button>
                                            <button
                                                onClick={handleDelete}
                                                disabled={isDeleting}
                                                className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 flex items-center"
                                            >
                                                {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                                {isDeleting ? 'Menghapus...' : 'Hapus'}
                                            </button>
                                        </div>
                                    </Dialog.Panel>
                                </div>
                            </Dialog>
                        </div>
                    </div>
                </div>
            </div>

            {/* Create Anggota Modal */}
            <CreateAnggota show={showCreateModal} onClose={() => setShowCreateModal(false)} />

            {/* Edit Anggota Modal */}
            {selectedUser && (
                <EditAnggota 
                    key={`edit-${selectedUser.id}`} 
                    show={showEditModal} 
                    onClose={() => setShowEditModal(false)} 
                    user={selectedUser} 
                />
            )}
        </AuthenticatedLayout>
    );
}