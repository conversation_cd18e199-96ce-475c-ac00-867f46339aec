import { Head, useForm } from '@inertiajs/react';
import { useRef, useEffect } from 'react';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Modal from '../../Components/Modal';
import TextInput from '../../Components/TextInput';
import InputError from '../../Components/InputError';
import PrimaryButton from '../../Components/PrimaryButton';
import SecondaryButton from '../../Components/SecondaryButton';

export default function CreateAnggota({ show, onClose }) {
    const { data, setData, post, processing, errors, reset } = useForm({
        nama: '',
        nik: '',
        email: '',
        bagian: 'Umum'
    });

    const submit = (e) => {
        e.preventDefault();
        post(route('admin.anggota.store'), {
            onSuccess: () => {
                toast.success('Data anggota berhasil ditambahkan');
                reset();
                onClose();
                // Tidak perlu redirect karena kita menggunakan modal
                // Inertia akan otomatis me-reload data karena kita menggunakan preserveScroll
            },
            onError: () => {
                toast.error('Gagal menambahkan data anggota');
            },
            preserveScroll: true
        });
    };

    return (
        <Modal show={show} onClose={onClose} maxWidth="md">
            <div className="p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Tambah Anggota Baru</h2>
                
                <form onSubmit={submit}>
                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Nama Lengkap</label>
                            <TextInput
                                type="text"
                                value={data.nama}
                                onChange={(e) => setData('nama', e.target.value)}
                                className="mt-1 block w-full"
                                required
                            />
                            <InputError message={errors.nama} className="mt-1" />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">NIK</label>
                            <TextInput
                                type="text"
                                value={data.nik}
                                onChange={(e) => setData('nik', e.target.value)}
                                className="mt-1 block w-full"
                                required
                            />
                            <InputError message={errors.nik} className="mt-1" />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">Bagian</label>
                            <TextInput
                                type="text"
                                value={data.bagian}
                                onChange={(e) => setData('bagian', e.target.value)}
                                className="mt-1 block w-full"
                                required
                            />
                            <InputError message={errors.bagian} className="mt-1" />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">Email</label>
                            <TextInput
                                type="email"
                                value={data.email}
                                onChange={(e) => setData('email', e.target.value)}
                                className="mt-1 block w-full"
                            />
                            <InputError message={errors.email} className="mt-1" />
                        </div>
                    </div>

                    <div className="flex justify-end space-x-3 mt-6">
                        <SecondaryButton
                            type="button"
                            onClick={onClose}
                            className="bg-gray-400 hover:bg-gray-500"
                        >
                            Batal
                        </SecondaryButton>
                        <PrimaryButton type="submit" disabled={processing}>
                            {processing ? 'Menyimpan...' : 'Simpan'}
                        </PrimaryButton>
                    </div>
                </form>
            </div>
        </Modal>
    );
}
