import { Head, useForm } from '@inertiajs/react';
import { useState, useEffect, useRef } from 'react';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Modal from '../../Components/Modal';
import TextInput from '../../Components/TextInput';
import InputError from '../../Components/InputError';
import PrimaryButton from '../../Components/PrimaryButton';
import SecondaryButton from '../../Components/SecondaryButton';

export default function EditAnggota({ show, onClose, user: propUser }) {
    const formRef = useRef(null);
    const nonAktifSectionRef = useRef(null);
    const [currentUser, setCurrentUser] = useState(propUser);
    
    // Fungsi untuk memformat tanggal ke format YYYY-MM-DD
    const formatDateForInput = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toISOString().split('T')[0];
    };

    const { data, setData, put, processing, errors, reset } = useForm({
        name: '',
        nik: '',
        email: '',
        bagian: 'Umum',
        status: 'Aktif',
        tanggal_masuk: new Date().toISOString().split('T')[0],
        tanggal_keluar: '',
        alasan_keluar: ''
    });

    // Update form when show or propUser changes
    useEffect(() => {
        if (show && propUser) {
            // Always update current user when modal is shown
            setCurrentUser(propUser);
            
            // Set form data directly using setData for each field
            setData('name', propUser.name || '');
            setData('nik', propUser.nik || '');
            setData('email', propUser.email || '');
            setData('bagian', propUser.bagian || 'Umum');
            setData('status', propUser.status || 'Aktif');
            setData('tanggal_masuk', propUser.tanggal_masuk ? formatDateForInput(propUser.tanggal_masuk) : new Date().toISOString().split('T')[0]);
            setData('tanggal_keluar', propUser.tanggal_keluar ? formatDateForInput(propUser.tanggal_keluar) : '');
            setData('alasan_keluar', propUser.alasan_keluar || '');
        }
    }, [show, propUser, reset]);
    
    useEffect(() => {
        // Scroll ke bagian non-aktif ketika status berubah menjadi Non-Aktif
        if (data.status === 'Non-Aktif' && nonAktifSectionRef.current) {
            nonAktifSectionRef.current.scrollIntoView({ 
                behavior: 'smooth',
                block: 'start'
            });
        }
    }, [data.status]);

    const submit = (e) => {
        e.preventDefault();
        if (!currentUser) return;
        
        put(route('admin.anggota.update', currentUser.id), {
            onSuccess: () => {
                toast.success('Data anggota berhasil diperbarui');
                onClose();
                // Tidak perlu redirect karena kita menggunakan modal
                // window.location.reload(); // Uncomment jika perlu refresh data
            },
            onError: () => {
                toast.error('Gagal memperbarui data anggota');
            },
            preserveScroll: true
        });
    };

    return (
        <Modal show={show} onClose={onClose} maxWidth="2xl">
            <div className="bg-white rounded-lg w-full max-w-2xl mx-auto">
                <div className="p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-6">Edit Data Anggota</h2>
                    
                    <form onSubmit={submit}>
                        <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-2">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Nama Lengkap</label>
                            <div>
                                <TextInput
                                    type="text"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    className="mt-1 block w-full"
                                    required
                                />

                            </div>
                            <InputError message={errors.name} className="mt-1" />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">NIK</label>
                            <div>
                                <TextInput
                                    type="text"
                                    value={data.nik}
                                    onChange={(e) => setData('nik', e.target.value)}
                                    className="mt-1 block w-full"
                                    required
                                    disabled
                                />

                            </div>
                            <InputError message={errors.nik} className="mt-1" />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">Bagian</label>
                            <TextInput
                                type="text"
                                value={data.bagian}
                                onChange={(e) => setData('bagian', e.target.value)}
                                className="mt-1 block w-full"
                                required
                            />
                            <InputError message={errors.bagian} className="mt-1" />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">Email</label>
                            <div>
                                <TextInput
                                    type="email"
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    className="mt-1 block w-full"
                                />

                            </div>
                            <InputError message={errors.email} className="mt-1" />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">Status</label>
                            <select
                                value={data.status}
                                onChange={(e) => {
                                    setData('status', e.target.value);
                                }}
                                className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                            >
                                <option value="Aktif">Aktif</option>
                                <option value="Non-Aktif">Non-Aktif</option>
                            </select>
                            <InputError message={errors.status} className="mt-1" />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700">Tanggal Masuk</label>
                            <TextInput
                                type="date"
                                value={data.tanggal_masuk}
                                onChange={(e) => setData('tanggal_masuk', e.target.value)}
                                className="mt-1 block w-full"
                                required
                            />
                            <InputError message={errors.tanggal_masuk} className="mt-1" />
                        </div>

                        {data.status === 'Non-Aktif' && (
                            <div ref={nonAktifSectionRef} className="pt-2">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Tanggal Keluar</label>
                                    <TextInput
                                        type="date"
                                        value={data.tanggal_keluar}
                                        onChange={(e) => setData('tanggal_keluar', e.target.value)}
                                        className="mt-1 block w-full"
                                        required={data.status === 'Non-Aktif'}
                                    />
                                    <InputError message={errors.tanggal_keluar} className="mt-1" />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Alasan Keluar</label>
                                    <textarea
                                        value={data.alasan_keluar}
                                        onChange={(e) => setData('alasan_keluar', e.target.value)}
                                        className="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                        rows={3}
                                        required={data.status === 'Non-Aktif'}
                                    />
                                    <InputError message={errors.alasan_keluar} className="mt-1" />
                                </div>
                            </div>
                        )}
                        </div>

                        <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                            <SecondaryButton
                                type="button"
                                onClick={onClose}
                                className="bg-gray-400 hover:bg-gray-500"
                            >
                                Batal
                            </SecondaryButton>
                            <PrimaryButton type="submit" disabled={processing}>
                                {processing ? 'Menyimpan...' : 'Simpan Perubahan'}
                            </PrimaryButton>
                        </div>
                    </form>
                </div>
            </div>
        </Modal>
    );
}
