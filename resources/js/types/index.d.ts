// Tipe untuk Toast
declare type ToastType = 'success' | 'error' | 'warning' | 'info';

declare interface ToastOptions {
  message: string;
  type?: ToastType;
  duration?: number;
}

// Tipe untuk Confirm Dialog
declare interface ConfirmDialogOptions {
  title?: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  isDanger?: boolean;
}

// Context Types
declare interface ToastContextType {
  showToast: (message: string, type?: ToastType) => void;
}

declare interface ConfirmDialogContextType {
  showConfirm: (options: ConfirmDialogOptions) => Promise<boolean>;
}
