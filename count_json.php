<?php
$file = __DIR__ . '/database/seeders/Anggota.json';

// Check if file exists and is readable
if (!file_exists($file)) {
    die("Error: File not found at $file");
}

// Get file contents
$content = file_get_contents($file);

// Check if file is empty
if (empty($content)) {
    die("The file is empty.");
}

// Try to decode JSON
$data = json_decode($content, true);

// Check for JSON errors
if (json_last_error() !== JSON_ERROR_NONE) {
    die("Invalid JSON: " . json_last_error_msg() . "\n");
}

// Count the data
$count = is_countable($data) ? count($data) : 0;

echo "Total data in Anggota.json: $count\n";
?>
